package com.zhentao.config;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Redis性能分析器
 * 分析Redis性能瓶颈，提供优化建议
 */
public class RedisPerformanceAnalyzer {
    
    private final JedisPool jedisPool;
    private final Map<String, Object> analysisResults = new ConcurrentHashMap<>();
    
    public RedisPerformanceAnalyzer(String host, int port, String password) {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(50);
        config.setMaxIdle(20);
        config.setMinIdle(10);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        
        if (password != null && !password.isEmpty()) {
            this.jedisPool = new JedisPool(config, host, port, 3000, password);
        } else {
            this.jedisPool = new JedisPool(config, host, port, 3000);
        }
        
        System.out.println("🔍 Redis性能分析器初始化完成");
    }
    
    /**
     * 执行完整的性能分析
     */
    public void performCompleteAnalysis() {
//        System.out.println("\n" + "=" .repeat(80));
        System.out.println("🚀 开始Redis性能分析");
//        System.out.println("=" .repeat(80));
        
        try {
            analyzeBasicInfo();
            analyzeMemoryUsage();
            analyzeSlowLog();
            analyzeKeyDistribution();
            analyzeConnectionInfo();
            analyzeReplicationInfo();
            analyzePersistenceInfo();
            analyzeCommandStats();
            
            generateOptimizationReport();
            
        } catch (Exception e) {
            System.err.println("❌ 性能分析过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析基本信息
     */
    private void analyzeBasicInfo() {
        System.out.println("\n📊 基本信息分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("server");
            Map<String, String> serverInfo = parseInfo(info);
            
            System.out.println("Redis版本: " + serverInfo.get("redis_version"));
            System.out.println("运行模式: " + serverInfo.get("redis_mode"));
            System.out.println("架构: " + serverInfo.get("arch_bits") + "位");
            System.out.println("运行时间: " + formatUptime(serverInfo.get("uptime_in_seconds")));
            System.out.println("配置文件: " + serverInfo.getOrDefault("config_file", "未指定"));
            
            analysisResults.put("server_info", serverInfo);
            
            // 检查版本
            String version = serverInfo.get("redis_version");
            if (compareVersion(version, "6.0.0") < 0) {
                System.out.println("⚠️  建议升级到Redis 6.0+以获得更好的性能");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 获取基本信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析内存使用情况
     */
    private void analyzeMemoryUsage() {
        System.out.println("\n💾 内存使用分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("memory");
            Map<String, String> memoryInfo = parseInfo(info);
            
            long usedMemory = Long.parseLong(memoryInfo.get("used_memory"));
            long usedMemoryRss = Long.parseLong(memoryInfo.get("used_memory_rss"));
            long usedMemoryPeak = Long.parseLong(memoryInfo.get("used_memory_peak"));
            
            System.out.println("已使用内存: " + formatBytes(usedMemory));
            System.out.println("RSS内存: " + formatBytes(usedMemoryRss));
            System.out.println("峰值内存: " + formatBytes(usedMemoryPeak));
            
            // 内存碎片率
            double fragmentationRatio = Double.parseDouble(memoryInfo.get("mem_fragmentation_ratio"));
            System.out.println("内存碎片率: " + String.format("%.2f", fragmentationRatio));
            
            if (fragmentationRatio > 1.5) {
                System.out.println("⚠️  内存碎片率过高，建议重启Redis或使用MEMORY PURGE命令");
            }
            
            // 内存使用详情
            analyzeMemoryDetails(jedis);
            
            analysisResults.put("memory_info", memoryInfo);
            analysisResults.put("fragmentation_ratio", fragmentationRatio);
            
        } catch (Exception e) {
            System.err.println("❌ 内存分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析内存使用详情
     */
    private void analyzeMemoryDetails(Jedis jedis) {
        try {
            // 获取数据库大小
            long dbSize = jedis.dbSize();
            System.out.println("键总数: " + dbSize);
            
            // 分析过期键
            String info = jedis.info("keyspace");
            if (!info.trim().isEmpty()) {
                System.out.println("数据库信息:");
                String[] lines = info.split("\r\n");
                for (String line : lines) {
                    if (line.startsWith("db")) {
                        System.out.println("  " + line);
                    }
                }
            }
            
            // 检查大键
            analyzeLargeKeys(jedis);
            
        } catch (Exception e) {
            System.err.println("❌ 内存详情分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析大键
     */
    private void analyzeLargeKeys(Jedis jedis) {
        System.out.println("\n🔍 大键分析 (采样分析):");
        
        try {
            Set<String> keys = jedis.keys("*");
            List<KeyInfo> largeKeys = new ArrayList<>();
            
            // 采样分析，避免阻塞
            int sampleSize = Math.min(keys.size(), 1000);
            List<String> sampleKeys = new ArrayList<>(keys).subList(0, sampleSize);
            
            for (String key : sampleKeys) {
                try {
                    String type = jedis.type(key);
                    long size = getKeySize(jedis, key, type);
                    long ttl = jedis.ttl(key);
                    
                    if (size > 1024 * 1024) { // 大于1MB的键
                        largeKeys.add(new KeyInfo(key, type, size, ttl));
                    }
                } catch (Exception e) {
                    // 忽略单个键的错误
                }
            }
            
            // 排序并显示前10个大键
            largeKeys.sort((a, b) -> Long.compare(b.size, a.size));
            
            if (largeKeys.isEmpty()) {
                System.out.println("  未发现大键 (>1MB)");
            } else {
                System.out.println("  发现 " + largeKeys.size() + " 个大键:");
                for (int i = 0; i < Math.min(10, largeKeys.size()); i++) {
                    KeyInfo keyInfo = largeKeys.get(i);
                    System.out.printf("    %s (%s): %s, TTL: %ds%n", 
                            keyInfo.key, keyInfo.type, formatBytes(keyInfo.size), keyInfo.ttl);
                }
            }
            
            analysisResults.put("large_keys", largeKeys);
            
        } catch (Exception e) {
            System.err.println("❌ 大键分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取键的大小
     */
    private long getKeySize(Jedis jedis, String key, String type) {
        try {
            switch (type.toLowerCase()) {
                case "string":
                    return jedis.strlen(key);
                case "list":
                    return jedis.llen(key);
                case "set":
                    return jedis.scard(key);
                case "zset":
                    return jedis.zcard(key);
                case "hash":
                    return jedis.hlen(key);
                default:
                    return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 分析慢查询日志
     */
    private void analyzeSlowLog() {
        System.out.println("\n🐌 慢查询分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            List<Object> slowLogs = Collections.singletonList(jedis.slowlogGet(10));
            
            if (slowLogs.isEmpty()) {
                System.out.println("✅ 未发现慢查询");
            } else {
                System.out.println("发现 " + slowLogs.size() + " 条慢查询:");
                
                for (Object logObj : slowLogs) {
                    if (logObj instanceof List) {
                        List<Object> log = (List<Object>) logObj;
                        if (log.size() >= 4) {
                            long id = (Long) log.get(0);
                            long timestamp = (Long) log.get(1);
                            long duration = (Long) log.get(2);
                            List<String> command = (List<String>) log.get(3);
                            
                            System.out.printf("  ID: %d, 耗时: %dμs, 命令: %s%n", 
                                    id, duration, String.join(" ", command));
                        }
                    }
                }
                
                System.out.println("⚠️  建议优化慢查询命令");
            }
            
            analysisResults.put("slow_logs", slowLogs);
            
        } catch (Exception e) {
            System.err.println("❌ 慢查询分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析键分布情况
     */
    private void analyzeKeyDistribution() {
        System.out.println("\n🔑 键分布分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            Set<String> keys = jedis.keys("*");
            Map<String, Integer> typeCount = new HashMap<>();
            Map<String, Integer> prefixCount = new HashMap<>();
            
            // 采样分析
            int sampleSize = Math.min(keys.size(), 1000);
            List<String> sampleKeys = new ArrayList<>(keys).subList(0, sampleSize);
            
            for (String key : sampleKeys) {
                try {
                    String type = jedis.type(key);
                    typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
                    
                    // 分析键前缀
                    String prefix = extractPrefix(key);
                    prefixCount.put(prefix, prefixCount.getOrDefault(prefix, 0) + 1);
                } catch (Exception e) {
                    // 忽略单个键的错误
                }
            }
            
            System.out.println("数据类型分布:");
            typeCount.forEach((type, count) -> 
                    System.out.printf("  %s: %d (%.1f%%)%n", type, count, 
                            100.0 * count / sampleKeys.size()));
            
            System.out.println("\n键前缀分布 (Top 10):");
            prefixCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(10)
                    .forEach(entry -> 
                            System.out.printf("  %s: %d%n", entry.getKey(), entry.getValue()));
            
            analysisResults.put("type_distribution", typeCount);
            analysisResults.put("prefix_distribution", prefixCount);
            
        } catch (Exception e) {
            System.err.println("❌ 键分布分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析连接信息
     */
    private void analyzeConnectionInfo() {
        System.out.println("\n🔗 连接信息分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("clients");
            Map<String, String> clientInfo = parseInfo(info);
            
            int connectedClients = Integer.parseInt(clientInfo.get("connected_clients"));
            int maxClients = Integer.parseInt(clientInfo.getOrDefault("maxclients", "10000"));
            
            System.out.println("当前连接数: " + connectedClients);
            System.out.println("最大连接数: " + maxClients);
            System.out.println("连接使用率: " + String.format("%.1f%%", 100.0 * connectedClients / maxClients));
            
            if (connectedClients > maxClients * 0.8) {
                System.out.println("⚠️  连接数接近上限，建议增加maxclients配置");
            }
            
            analysisResults.put("client_info", clientInfo);
            
        } catch (Exception e) {
            System.err.println("❌ 连接信息分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析复制信息
     */
    private void analyzeReplicationInfo() {
        System.out.println("\n🔄 复制信息分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("replication");
            Map<String, String> replInfo = parseInfo(info);
            
            String role = replInfo.get("role");
            System.out.println("角色: " + role);
            
            if ("master".equals(role)) {
                int connectedSlaves = Integer.parseInt(replInfo.getOrDefault("connected_slaves", "0"));
                System.out.println("连接的从节点数: " + connectedSlaves);
                
                // 显示从节点信息
                for (int i = 0; i < connectedSlaves; i++) {
                    String slaveInfo = replInfo.get("slave" + i);
                    if (slaveInfo != null) {
                        System.out.println("  从节点" + i + ": " + slaveInfo);
                    }
                }
            } else if ("slave".equals(role)) {
                String masterHost = replInfo.get("master_host");
                String masterPort = replInfo.get("master_port");
                String linkStatus = replInfo.get("master_link_status");
                
                System.out.println("主节点: " + masterHost + ":" + masterPort);
                System.out.println("连接状态: " + linkStatus);
                
                if (!"up".equals(linkStatus)) {
                    System.out.println("⚠️  主从连接异常");
                }
            }
            
            analysisResults.put("replication_info", replInfo);
            
        } catch (Exception e) {
            System.err.println("❌ 复制信息分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析持久化信息
     */
    private void analyzePersistenceInfo() {
        System.out.println("\n💾 持久化信息分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("persistence");
            Map<String, String> persistInfo = parseInfo(info);
            
            // RDB信息
            String rdbLastSave = persistInfo.get("rdb_last_save_time");
            String rdbLastBgsaveStatus = persistInfo.get("rdb_last_bgsave_status");
            
            System.out.println("RDB最后保存时间: " + formatTimestamp(rdbLastSave));
            System.out.println("RDB最后备份状态: " + rdbLastBgsaveStatus);
            
            // AOF信息
            String aofEnabled = persistInfo.get("aof_enabled");
            if ("1".equals(aofEnabled)) {
                String aofLastRewriteStatus = persistInfo.get("aof_last_rewrite_status");
                String aofCurrentSize = persistInfo.get("aof_current_size");
                
                System.out.println("AOF已启用");
                System.out.println("AOF最后重写状态: " + aofLastRewriteStatus);
                System.out.println("AOF当前大小: " + formatBytes(Long.parseLong(aofCurrentSize)));
            } else {
                System.out.println("AOF未启用");
            }
            
            analysisResults.put("persistence_info", persistInfo);
            
        } catch (Exception e) {
            System.err.println("❌ 持久化信息分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析命令统计
     */
    private void analyzeCommandStats() {
        System.out.println("\n📈 命令统计分析");
//        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("commandstats");
            Map<String, String> cmdStats = parseInfo(info);
            
            List<CommandStat> commandStats = new ArrayList<>();
            
            for (Map.Entry<String, String> entry : cmdStats.entrySet()) {
                if (entry.getKey().startsWith("cmdstat_")) {
                    String command = entry.getKey().substring(8);
                    String stats = entry.getValue();
                    
                    // 解析统计信息: calls=xxx,usec=xxx,usec_per_call=xxx
                    Pattern pattern = Pattern.compile("calls=(\\d+),usec=(\\d+),usec_per_call=([\\d.]+)");
                    Matcher matcher = pattern.matcher(stats);
                    
                    if (matcher.find()) {
                        long calls = Long.parseLong(matcher.group(1));
                        long usec = Long.parseLong(matcher.group(2));
                        double usecPerCall = Double.parseDouble(matcher.group(3));
                        
                        commandStats.add(new CommandStat(command, calls, usec, usecPerCall));
                    }
                }
            }
            
            // 按调用次数排序
            commandStats.sort((a, b) -> Long.compare(b.calls, a.calls));
            
            System.out.println("命令使用统计 (Top 10):");
            for (int i = 0; i < Math.min(10, commandStats.size()); i++) {
                CommandStat stat = commandStats.get(i);
                System.out.printf("  %s: %d次, 平均耗时: %.2fμs%n", 
                        stat.command, stat.calls, stat.usecPerCall);
            }
            
            analysisResults.put("command_stats", commandStats);
            
        } catch (Exception e) {
            System.err.println("❌ 命令统计分析失败: " + e.getMessage());
        }
    }
    
    // 辅助方法
    private Map<String, String> parseInfo(String info) {
        Map<String, String> result = new HashMap<>();
        String[] lines = info.split("\r\n");
        
        for (String line : lines) {
            if (line.contains(":") && !line.startsWith("#")) {
                String[] parts = line.split(":", 2);
                if (parts.length == 2) {
                    result.put(parts[0], parts[1]);
                }
            }
        }
        
        return result;
    }
    
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + "B";
        if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024));
        return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
    }
    
    private String formatUptime(String seconds) {
        long sec = Long.parseLong(seconds);
        long days = sec / (24 * 3600);
        long hours = (sec % (24 * 3600)) / 3600;
        long minutes = (sec % 3600) / 60;
        
        return String.format("%d天%d小时%d分钟", days, hours, minutes);
    }
    
    private String formatTimestamp(String timestamp) {
        if (timestamp == null) return "未知";
        long ts = Long.parseLong(timestamp);
        return new Date(ts * 1000).toString();
    }
    
    private int compareVersion(String v1, String v2) {
        String[] parts1 = v1.split("\\.");
        String[] parts2 = v2.split("\\.");
        
        int maxLength = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < maxLength; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            
            if (num1 != num2) {
                return Integer.compare(num1, num2);
            }
        }
        return 0;
    }
    
    private String extractPrefix(String key) {
        int colonIndex = key.indexOf(':');
        return colonIndex > 0 ? key.substring(0, colonIndex) : key;
    }
    
    /**
     * 生成优化报告
     */
    private void generateOptimizationReport() {
//        System.out.println("\n" + "=" .repeat(80));
        System.out.println("📋 性能优化建议报告");
//        System.out.println("=" .repeat(80));
        
        List<String> recommendations = new ArrayList<>();
        
        // 内存优化建议
        Double fragmentationRatio = (Double) analysisResults.get("fragmentation_ratio");
        if (fragmentationRatio != null && fragmentationRatio > 1.5) {
            recommendations.add("内存碎片率过高(" + String.format("%.2f", fragmentationRatio) + 
                    ")，建议重启Redis或执行MEMORY PURGE");
        }
        
        // 大键优化建议
        @SuppressWarnings("unchecked")
        List<KeyInfo> largeKeys = (List<KeyInfo>) analysisResults.get("large_keys");
        if (largeKeys != null && !largeKeys.isEmpty()) {
            recommendations.add("发现" + largeKeys.size() + "个大键，建议拆分或使用更高效的数据结构");
        }
        
        // 慢查询优化建议
        @SuppressWarnings("unchecked")
        List<Object> slowLogs = (List<Object>) analysisResults.get("slow_logs");
        if (slowLogs != null && !slowLogs.isEmpty()) {
            recommendations.add("发现" + slowLogs.size() + "条慢查询，建议优化相关命令");
        }
        
        // 连接数优化建议
        @SuppressWarnings("unchecked")
        Map<String, String> clientInfo = (Map<String, String>) analysisResults.get("client_info");
        if (clientInfo != null) {
            int connectedClients = Integer.parseInt(clientInfo.get("connected_clients"));
            int maxClients = Integer.parseInt(clientInfo.getOrDefault("maxclients", "10000"));
            if (connectedClients > maxClients * 0.8) {
                recommendations.add("连接数使用率过高，建议增加maxclients配置或使用连接池");
            }
        }
        
        if (recommendations.isEmpty()) {
            System.out.println("✅ 未发现明显的性能问题，Redis运行状态良好");
        } else {
            System.out.println("发现以下性能优化建议:");
            for (int i = 0; i < recommendations.size(); i++) {
                System.out.println((i + 1) + ". " + recommendations.get(i));
            }
        }
    }
    
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
    }
    
    // 内部类
    private static class KeyInfo {
        final String key;
        final String type;
        final long size;
        final long ttl;
        
        KeyInfo(String key, String type, long size, long ttl) {
            this.key = key;
            this.type = type;
            this.size = size;
            this.ttl = ttl;
        }
    }
    
    private static class CommandStat {
        final String command;
        final long calls;
        final long usec;
        final double usecPerCall;
        
        CommandStat(String command, long calls, long usec, double usecPerCall) {
            this.command = command;
            this.calls = calls;
            this.usec = usec;
            this.usecPerCall = usecPerCall;
        }
    }
}