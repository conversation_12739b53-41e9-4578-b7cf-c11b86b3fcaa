<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis持久化配置管理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #117a8b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Redis持久化配置管理</h1>
        
        <!-- 状态查看区域 -->
        <div class="section">
            <h2>📊 状态查看</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="getPersistenceStatus()">查看持久化状态</button>
                <button class="btn-primary" onclick="getPersistenceStats()">查看详细统计</button>
                <button class="btn-primary" onclick="getAllPersistenceConfig()">查看完整配置</button>
            </div>
        </div>

        <!-- RDB配置区域 -->
        <div class="section">
            <h2>💾 RDB配置</h2>
            <div class="input-group">
                <label>保存间隔(秒):</label>
                <input type="number" id="rdbSeconds" value="900" min="1">
            </div>
            <div class="input-group">
                <label>变化数量:</label>
                <input type="number" id="rdbChanges" value="1" min="1">
            </div>
            <div class="button-group">
                <button class="btn-success" onclick="configureRDB()">配置RDB</button>
                <button class="btn-info" onclick="getRDBConfig()">查看RDB配置</button>
                <button class="btn-warning" onclick="triggerRDBSave()">手动保存RDB</button>
            </div>
        </div>

        <!-- AOF配置区域 -->
        <div class="section">
            <h2>📝 AOF配置</h2>
            <div class="input-group">
                <label>启用AOF:</label>
                <select id="aofEnable">
                    <option value="true">启用</option>
                    <option value="false">禁用</option>
                </select>
            </div>
            <div class="input-group">
                <label>同步策略:</label>
                <select id="aofFsync">
                    <option value="everysec">每秒同步</option>
                    <option value="always">总是同步</option>
                    <option value="no">不同步</option>
                </select>
            </div>
            <div class="button-group">
                <button class="btn-success" onclick="configureAOF()">配置AOF</button>
                <button class="btn-info" onclick="getAOFConfig()">查看AOF配置</button>
                <button class="btn-warning" onclick="triggerAOFRewrite()">AOF重写</button>
            </div>
        </div>

        <!-- 高级查看区域 -->
        <div class="section">
            <h2>🔍 高级查看</h2>
            <div class="input-group">
                <label>配置模式:</label>
                <input type="text" id="configPattern" value="*save*" placeholder="如: *save*, *aof*, *rdb*">
            </div>
            <div class="button-group">
                <button class="btn-info" onclick="getConfigByPattern()">按模式查看</button>
                <button class="btn-info" onclick="getConfig('*save*')">Save配置</button>
                <button class="btn-info" onclick="getConfig('*aof*')">AOF配置</button>
                <button class="btn-info" onclick="getConfig('*rdb*')">RDB配置</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="section">
            <h2>📋 执行结果</h2>
            <div id="result" class="result">点击上方按钮查看Redis持久化配置信息...</div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:1000';

        function showResult(data) {
            document.getElementById('result').textContent = data;
        }

        function showError(error) {
            document.getElementById('result').textContent = '错误: ' + error;
        }

        async function makeRequest(url) {
            try {
                const response = await fetch(url);
                const data = await response.text();
                showResult(data);
            } catch (error) {
                showError(error.message);
            }
        }

        // 状态查看函数
        function getPersistenceStatus() {
            makeRequest(`${baseUrl}/redis/status`);
        }

        function getPersistenceStats() {
            makeRequest(`${baseUrl}/redis/stats`);
        }

        function getAllPersistenceConfig() {
            makeRequest(`${baseUrl}/redis/persistence/all`);
        }

        // RDB配置函数
        function configureRDB() {
            const seconds = document.getElementById('rdbSeconds').value;
            const changes = document.getElementById('rdbChanges').value;
            makeRequest(`${baseUrl}/RDB?saveSeconds=${seconds}&changedKeys=${changes}`);
        }

        function getRDBConfig() {
            makeRequest(`${baseUrl}/redis/rdb/config`);
        }

        function triggerRDBSave() {
            makeRequest(`${baseUrl}/RDB/save?async=true`);
        }

        // AOF配置函数
        function configureAOF() {
            const enable = document.getElementById('aofEnable').value;
            const fsync = document.getElementById('aofFsync').value;
            makeRequest(`${baseUrl}/AOF?enable=${enable}&fsync=${fsync}`);
        }

        function getAOFConfig() {
            makeRequest(`${baseUrl}/redis/aof/config`);
        }

        function triggerAOFRewrite() {
            makeRequest(`${baseUrl}/AOP/restart`);
        }

        // 高级查看函数
        function getConfigByPattern() {
            const pattern = document.getElementById('configPattern').value;
            makeRequest(`${baseUrl}/redis/config?pattern=${encodeURIComponent(pattern)}`);
        }

        function getConfig(pattern) {
            makeRequest(`${baseUrl}/redis/config?pattern=${encodeURIComponent(pattern)}`);
        }

        // 页面加载时自动获取状态
        window.onload = function() {
            getPersistenceStatus();
        };
    </script>
</body>
</html>
