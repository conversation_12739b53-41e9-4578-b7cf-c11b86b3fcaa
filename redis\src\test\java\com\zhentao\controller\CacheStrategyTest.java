package com.zhentao.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 缓存策略测试类
 */
@WebMvcTest(Controller.class)
public class CacheStrategyTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RedisTemplate<String, String> redisTemplate;

    @MockBean
    private ValueOperations<String, String> valueOperations;

    @Test
    public void testCachePenetrationProtection() throws Exception {
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("user:1")).thenReturn("用户1数据");

        // 测试缓存穿透防护
        mockMvc.perform(get("/cache/penetration-protection")
                .param("key", "user:1"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("缓存命中")));
    }

    @Test
    public void testCacheBreakdownProtection() throws Exception {
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("user:2")).thenReturn(null);

        // 测试缓存击穿防护
        mockMvc.perform(get("/cache/breakdown-protection")
                .param("key", "user:2"))
                .andExpect(status().isOk());
    }

    @Test
    public void testCacheAvalancheProtection() throws Exception {
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("product:1")).thenReturn(null);

        // 测试缓存雪崩防护
        mockMvc.perform(get("/cache/avalanche-protection")
                .param("key", "product:1"))
                .andExpect(status().isOk());
    }

    @Test
    public void testComprehensiveProtection() throws Exception {
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("user:100")).thenReturn(null);

        // 测试综合防护策略
        mockMvc.perform(get("/cache/comprehensive-protection")
                .param("key", "user:100"))
                .andExpect(status().isOk());
    }

    @Test
    public void testCacheWarmup() throws Exception {
        // 测试缓存预热
        mockMvc.perform(get("/cache/warmup")
                .param("keys", "user:1,user:2,product:1"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("预热完成")));
    }

    @Test
    public void testDegradeMode() throws Exception {
        // 测试降级模式开启
        mockMvc.perform(get("/cache/degrade")
                .param("enable", "true"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("降级模式已开启")));

        // 测试降级模式关闭
        mockMvc.perform(get("/cache/degrade")
                .param("enable", "false"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("降级模式已关闭")));
    }

    @Test
    public void testCacheStats() throws Exception {
        // 测试缓存统计信息
        mockMvc.perform(get("/cache/stats"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("缓存统计信息")));
    }

    @Test
    public void testCleanupLocks() throws Exception {
        // 测试清理锁
        mockMvc.perform(get("/cache/cleanup"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("清理完成")));
    }
}
