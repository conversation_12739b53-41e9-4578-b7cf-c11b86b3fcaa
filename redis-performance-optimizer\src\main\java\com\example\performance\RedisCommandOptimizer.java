package com.example.performance;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Redis命令优化器
 * 提供命令使用优化策略和最佳实践
 */
public class RedisCommandOptimizer {
    
    private final JedisPool jedisPool;
    
    public RedisCommandOptimizer(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }
    
    /**
     * 执行命令优化分析
     */
    public void performCommandOptimization() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("⚡ 开始Redis命令优化分析");
        System.out.println("=" .repeat(80));
        
        try {
            analyzeSlowCommands();
            demonstrateCommandOptimizations();
            analyzeKeyPatterns();
            demonstrateBestPractices();
            generateCommandOptimizationReport();
            
        } catch (Exception e) {
            System.err.println("❌ 命令优化分析过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析慢命令
     */
    private void analyzeSlowCommands() {
        System.out.println("\n🐌 慢命令分析");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            // 获取慢查询日志
            List<Object> slowLogs = jedis.slowlogGet(20);
            
            if (slowLogs.isEmpty()) {
                System.out.println("✅ 未发现慢命令");
                return;
            }
            
            Map<String, SlowCommandInfo> commandStats = new HashMap<>();
            
            for (Object logObj : slowLogs) {
                if (logObj instanceof List) {
                    List<Object> log = (List<Object>) logObj;
                    if (log.size() >= 4) {
                        long duration = (Long) log.get(2);
                        List<String> command = (List<String>) log.get(3);
                        String commandName = command.get(0).toUpperCase();
                        
                        SlowCommandInfo info = commandStats.getOrDefault(commandName, 
                                new SlowCommandInfo(commandName));
                        info.addExecution(duration);
                        commandStats.put(commandName, info);
                    }
                }
            }
            
            System.out.println("慢命令统计:");
            commandStats.values().stream()
                    .sorted((a, b) -> Long.compare(b.totalDuration, a.totalDuration))
                    .forEach(info -> {
                        System.out.printf("  %s: %d次, 总耗时: %dμs, 平均耗时: %.1fμs%n",
                                info.command, info.count, info.totalDuration, info.getAverageDuration());
                        
                        // 提供优化建议
                        provideCommandOptimizationSuggestion(info.command);
                    });
            
        } catch (Exception e) {
            System.err.println("❌ 慢命令分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 提供命令优化建议
     */
    private void provideCommandOptimizationSuggestion(String command) {
        switch (command.toUpperCase()) {
            case "KEYS":
                System.out.println("    建议: 使用SCAN命令替代KEYS，避免阻塞");
                break;
            case "FLUSHDB":
            case "FLUSHALL":
                System.out.println("    建议: 在低峰期执行，或使用FLUSHDB ASYNC");
                break;
            case "SORT":
                System.out.println("    建议: 考虑在应用层排序，或使用有序集合");
                break;
            case "SMEMBERS":
                System.out.println("    建议: 对于大集合，使用SSCAN分页获取");
                break;
            case "HGETALL":
                System.out.println("    建议: 对于大Hash，使用HSCAN或只获取需要的字段");
                break;
            case "LRANGE":
                System.out.println("    建议: 限制范围大小，避免一次获取过多元素");
                break;
            case "ZRANGE":
            case "ZREVRANGE":
                System.out.println("    建议: 使用分页，避免一次获取过多元素");
                break;
            default:
                System.out.println("    建议: 检查命令参数和数据量");
        }
    }
    
    /**
     * 演示命令优化
     */
    private void demonstrateCommandOptimizations() {
        System.out.println("\n⚡ 命令优化演示");
        System.out.println("-" .repeat(50));
        
        try {
            demonstrateKeysVsScan();
            demonstrateBatchOperations();
            demonstratePipelineUsage();
            demonstrateDataStructureOptimization();
            
        } catch (Exception e) {
            System.err.println("❌ 命令优化演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示KEYS vs SCAN
     */
    private void demonstrateKeysVsScan() {
        System.out.println("KEYS vs SCAN 性能对比:");
        
        try (Jedis jedis = jedisPool.getResource()) {
            // 准备测试数据
            int keyCount = 10000;
            System.out.println("准备 " + keyCount + " 个测试键...");
            
            Pipeline pipeline = jedis.pipelined();
            for (int i = 0; i < keyCount; i++) {
                pipeline.set("demo:keys:test:" + i, "value" + i);
            }
            pipeline.sync();
            
            // 测试KEYS命令
            long start = System.currentTimeMillis();
            Set<String> keysResult = jedis.keys("demo:keys:test:*");
            long keysTime = System.currentTimeMillis() - start;
            
            // 测试SCAN命令
            start = System.currentTimeMillis();
            Set<String> scanResult = new HashSet<>();
            String cursor = "0";
            ScanParams scanParams = new ScanParams().match("demo:keys:test:*").count(100);
            
            do {
                ScanResult<String> result = jedis.scan(cursor, scanParams);
                cursor = result.getCursor();
                scanResult.addAll(result.getResult());
            } while (!"0".equals(cursor));
            
            long scanTime = System.currentTimeMillis() - start;
            
            System.out.printf("  KEYS命令: %dms, 找到%d个键%n", keysTime, keysResult.size());
            System.out.printf("  SCAN命令: %dms, 找到%d个键%n", scanTime, scanResult.size());
            
            if (scanTime < keysTime) {
                System.out.println("  ✅ SCAN命令性能更好且不会阻塞Redis");
            } else {
                System.out.println("  ✅ SCAN命令虽然可能稍慢，但不会阻塞Redis，更适合生产环境");
            }
            
            // 清理测试数据
            pipeline = jedis.pipelined();
            for (String key : keysResult) {
                pipeline.del(key);
            }
            pipeline.sync();
            
        } catch (Exception e) {
            System.err.println("❌ KEYS vs SCAN 演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示批量操作
     */
    private void demonstrateBatchOperations() {
        System.out.println("\n批量操作优化:");
        
        try (Jedis jedis = jedisPool.getResource()) {
            int count = 1000;
            
            // 单个操作 vs 批量操作
            System.out.println("SET操作对比:");
            
            // 多个SET
            long start = System.currentTimeMillis();
            for (int i = 0; i < count; i++) {
                jedis.set("demo:batch:single:" + i, "value" + i);
            }
            long singleTime = System.currentTimeMillis() - start;
            
            // MSET
            String[] keyValues = new String[count * 2];
            for (int i = 0; i < count; i++) {
                keyValues[i * 2] = "demo:batch:multi:" + i;
                keyValues[i * 2 + 1] = "value" + i;
            }
            
            start = System.currentTimeMillis();
            jedis.mset(keyValues);
            long batchTime = System.currentTimeMillis() - start;
            
            System.out.printf("  单个SET: %dms%n", singleTime);
            System.out.printf("  MSET: %dms%n", batchTime);
            System.out.printf("  性能提升: %.1fx%n", (double) singleTime / batchTime);
            
            // 清理
            String[] keys = new String[count * 2];
            for (int i = 0; i < count; i++) {
                keys[i] = "demo:batch:single:" + i;
                keys[i + count] = "demo:batch:multi:" + i;
            }
            jedis.del(keys);
            
        } catch (Exception e) {
            System.err.println("❌ 批量操作演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示Pipeline使用
     */
    private void demonstratePipelineUsage() {
        System.out.println("\nPipeline使用优化:");
        
        try (Jedis jedis = jedisPool.getResource()) {
            int count = 1000;
            
            // 普通操作
            long start = System.currentTimeMillis();
            for (int i = 0; i < count; i++) {
                jedis.set("demo:pipeline:normal:" + i, "value" + i);
                jedis.expire("demo:pipeline:normal:" + i, 3600);
            }
            long normalTime = System.currentTimeMillis() - start;
            
            // Pipeline操作
            start = System.currentTimeMillis();
            Pipeline pipeline = jedis.pipelined();
            for (int i = 0; i < count; i++) {
                pipeline.set("demo:pipeline:pipe:" + i, "value" + i);
                pipeline.expire("demo:pipeline:pipe:" + i, 3600);
            }
            pipeline.sync();
            long pipelineTime = System.currentTimeMillis() - start;
            
            System.out.printf("  普通操作: %dms%n", normalTime);
            System.out.printf("  Pipeline操作: %dms%n", pipelineTime);
            System.out.printf("  性能提升: %.1fx%n", (double) normalTime / pipelineTime);
            
            // 清理
            pipeline = jedis.pipelined();
            for (int i = 0; i < count; i++) {
                pipeline.del("demo:pipeline:normal:" + i);
                pipeline.del("demo:pipeline:pipe:" + i);
            }
            pipeline.sync();
            
        } catch (Exception e) {
            System.err.println("❌ Pipeline演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示数据结构优化
     */
    private void demonstrateDataStructureOptimization() {
        System.out.println("\n数据结构优化:");
        
        try (Jedis jedis = jedisPool.getResource()) {
            // String vs Hash 存储用户信息
            demonstrateStringVsHash(jedis);
            
            // List vs Set 去重操作
            demonstrateListVsSet(jedis);
            
            // ZSet 排序操作
            demonstrateZSetSorting(jedis);
            
        } catch (Exception e) {
            System.err.println("❌ 数据结构优化演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示String vs Hash
     */
    private void demonstrateStringVsHash(Jedis jedis) {
        System.out.println("String vs Hash 存储对比:");
        
        int userCount = 1000;
        
        // 使用String存储
        long start = System.currentTimeMillis();
        for (int i = 0; i < userCount; i++) {
            jedis.set("user:string:" + i + ":name", "user" + i);
            jedis.set("user:string:" + i + ":age", String.valueOf(20 + i % 50));
            jedis.set("user:string:" + i + ":email", "user" + i + "@example.com");
        }
        long stringTime = System.currentTimeMillis() - start;
        
        // 使用Hash存储
        start = System.currentTimeMillis();
        for (int i = 0; i < userCount; i++) {
            Map<String, String> userInfo = new HashMap<>();
            userInfo.put("name", "user" + i);
            userInfo.put("age", String.valueOf(20 + i % 50));
            userInfo.put("email", "user" + i + "@example.com");
            jedis.hmset("user:hash:" + i, userInfo);
        }
        long hashTime = System.currentTimeMillis() - start;
        
        System.out.printf("  String存储: %dms%n", stringTime);
        System.out.printf("  Hash存储: %dms%n", hashTime);
        
        if (hashTime < stringTime) {
            System.out.println("  ✅ Hash存储更高效，且结构更清晰");
        }
        
        // 清理
        Pipeline pipeline = jedis.pipelined();
        for (int i = 0; i < userCount; i++) {
            pipeline.del("user:string:" + i + ":name");
            pipeline.del("user:string:" + i + ":age");
            pipeline.del("user:string:" + i + ":email");
            pipeline.del("user:hash:" + i);
        }
        pipeline.sync();
    }
    
    /**
     * 演示List vs Set
     */
    private void demonstrateListVsSet(Jedis jedis) {
        System.out.println("\nList vs Set 去重对比:");
        
        String listKey = "demo:list:dedup";
        String setKey = "demo:set:dedup";
        
        // 向List添加重复元素
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            jedis.lpush(listKey, "item" + (i % 100)); // 会有重复
        }
        
        // 从List去重
        Set<String> uniqueItems = new HashSet<>(jedis.lrange(listKey, 0, -1));
        long listTime = System.currentTimeMillis() - start;
        
        // 使用Set自动去重
        start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            jedis.sadd(setKey, "item" + (i % 100));
        }
        long setTime = System.currentTimeMillis() - start;
        
        System.out.printf("  List+去重: %dms, 唯一元素: %d%n", listTime, uniqueItems.size());
        System.out.printf("  Set自动去重: %dms, 唯一元素: %d%n", setTime, jedis.scard(setKey).intValue());
        System.out.println("  ✅ Set天然去重，更适合需要唯一性的场景");
        
        // 清理
        jedis.del(listKey, setKey);
    }
    
    /**
     * 演示ZSet排序
     */
    private void demonstrateZSetSorting(Jedis jedis) {
        System.out.println("\nZSet排序优化:");
        
        String zsetKey = "demo:zset:ranking";
        
        // 添加排序数据
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            double score = ThreadLocalRandom.current().nextDouble(0, 1000);
            jedis.zadd(zsetKey, score, "user" + i);
        }
        long addTime = System.currentTimeMillis() - start;
        
        // 获取排名
        start = System.currentTimeMillis();
        Set<String> topUsers = jedis.zrevrange(zsetKey, 0, 9); // 前10名
        long queryTime = System.currentTimeMillis() - start;
        
        System.out.printf("  添加1000个元素: %dms%n", addTime);
        System.out.printf("  查询前10名: %dms%n", queryTime);
        System.out.println("  ✅ ZSet提供高效的排序和范围查询");
        
        // 清理
        jedis.del(zsetKey);
    }
    
    /**
     * 分析键模式
     */
    private void analyzeKeyPatterns() {
        System.out.println("\n🔑 键模式分析");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            Map<String, Integer> patternCount = new HashMap<>();
            Set<String> longKeys = new HashSet<>();
            
            String cursor = "0";
            ScanParams scanParams = new ScanParams().count(100);
            
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                
                for (String key : scanResult.getResult()) {
                    // 分析键模式
                    String pattern = analyzeKeyPattern(key);
                    patternCount.put(pattern, patternCount.getOrDefault(pattern, 0) + 1);
                    
                    // 检查长键
                    if (key.length() > 100) {
                        longKeys.add(key);
                    }
                }
            } while (!"0".equals(cursor));
            
            System.out.println("键模式分布:");
            patternCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(10)
                    .forEach(entry -> 
                            System.out.printf("  %s: %d个键%n", entry.getKey(), entry.getValue()));
            
            if (!longKeys.isEmpty()) {
                System.out.println("\n发现过长键名 (>100字符): " + longKeys.size() + " 个");
                System.out.println("⚠️  建议缩短键名以节省内存和提高性能");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 键模式分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析键模式
     */
    private String analyzeKeyPattern(String key) {
        // 简单的模式分析，将数字替换为*
        return key.replaceAll("\\d+", "*");
    }
    
    /**
     * 演示最佳实践
     */
    private void demonstrateBestPractices() {
        System.out.println("\n💡 最佳实践演示");
        System.out.println("-" .repeat(50));
        
        System.out.println("1. 使用合适的数据类型:");
        System.out.println("   - 计数器: 使用INCR/DECR而不是GET+SET");
        System.out.println("   - 布尔值: 使用位操作或简单的0/1");
        System.out.println("   - 时间序列: 使用ZSet按时间戳排序");
        
        System.out.println("\n2. 避免大键:");
        System.out.println("   - String: 避免存储大于10MB的值");
        System.out.println("   - List/Set: 避免超过10000个元素");
        System.out.println("   - Hash: 避免超过1000个字段");
        
        System.out.println("\n3. 合理设置过期时间:");
        System.out.println("   - 缓存数据: 根据业务需求设置TTL");
        System.out.println("   - 临时数据: 及时清理避免内存泄漏");
        System.out.println("   - 会话数据: 设置合理的过期时间");
        
        System.out.println("\n4. 使用Pipeline和批量操作:");
        System.out.println("   - 批量读取: 使用MGET而不是多个GET");
        System.out.println("   - 批量写入: 使用MSET或Pipeline");
        System.out.println("   - 复杂操作: 使用Lua脚本保证原子性");
    }
    
    /**
     * 生成命令优化报告
     */
    private void generateCommandOptimizationReport() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("📋 命令优化建议报告");
        System.out.println("=" .repeat(80));
        
        List<String> recommendations = new ArrayList<>();
        
        recommendations.add("避免使用KEYS命令，使用SCAN替代");
        recommendations.add("使用批量操作(MGET/MSET)减少网络往返");
        recommendations.add("使用Pipeline进行批量操作");
        recommendations.add("选择合适的数据结构存储数据");
        recommendations.add("避免创建大键，合理拆分数据");
        recommendations.add("为所有键设置合理的过期时间");
        recommendations.add("使用Lua脚本保证复杂操作的原子性");
        recommendations.add("监控慢查询日志，及时优化慢命令");
        recommendations.add("使用Redis的原生命令而不是复杂的应用逻辑");
        recommendations.add("合理使用Redis的高级特性(HyperLogLog、Bloom Filter等)");
        
        System.out.println("命令优化建议:");
        for (int i = 0; i < recommendations.size(); i++) {
            System.out.println((i + 1) + ". " + recommendations.get(i));
        }
        
        System.out.println("\n性能优化要点:");
        System.out.println("- 减少网络往返次数");
        System.out.println("- 选择合适的数据结构");
        System.out.println("- 避免阻塞操作");
        System.out.println("- 合理使用内存");
        System.out.println("- 监控和分析性能指标");
    }
    
    // 内部类
    private static class SlowCommandInfo {
        final String command;
        int count = 0;
        long totalDuration = 0;
        
        SlowCommandInfo(String command) {
            this.command = command;
        }
        
        void addExecution(long duration) {
            count++;
            totalDuration += duration;
        }
        
        double getAverageDuration() {
            return count > 0 ? (double) totalDuration / count : 0;
        }
    }
}
