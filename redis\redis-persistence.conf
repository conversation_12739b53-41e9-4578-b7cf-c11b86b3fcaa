# Redis持久化配置文件
# 这个配置文件展示了Redis RDB和AOF持久化的完整配置

# ======================== RDB持久化配置 ========================

# RDB保存策略
# save <seconds> <changes>
# 在指定时间内，如果至少有指定数量的key发生变化，则保存数据库
save 900 1      # 900秒内至少1个key变化
save 300 10     # 300秒内至少10个key变化  
save 60 10000   # 60秒内至少10000个key变化

# RDB文件名
dbfilename dump.rdb

# RDB文件存储目录
dir ./

# 是否压缩RDB文件
# 压缩会消耗CPU但节省磁盘空间
rdbcompression yes

# 是否对RDB文件进行校验和检查
# 校验和会增加约10%的性能开销，但提供数据完整性保证
rdbchecksum yes

# 当RDB保存失败时是否停止写入
# 建议设置为yes，确保数据安全
stop-writes-on-bgsave-error yes

# ======================== AOF持久化配置 ========================

# 是否启用AOF持久化
appendonly yes

# AOF文件名
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每个写命令都立即同步到磁盘（最安全但性能最差）
# everysec: 每秒同步一次（推荐，平衡安全性和性能）
# no: 由操作系统决定何时同步（性能最好但可能丢失数据）
appendfsync everysec

# 在AOF重写时是否暂停fsync
# 设置为no可以避免延迟峰值
no-appendfsync-on-rewrite no

# AOF自动重写配置
# 当AOF文件大小超过上次重写后大小的指定百分比时触发重写
auto-aof-rewrite-percentage 100

# AOF重写的最小文件大小
# 只有当AOF文件大于此值时才会触发重写
auto-aof-rewrite-min-size 64mb

# AOF加载时是否忽略最后一个可能不完整的命令
aof-load-truncated yes

# 是否使用RDB-AOF混合持久化
# 在AOF重写时使用RDB格式保存数据，提高加载速度
aof-use-rdb-preamble yes

# ======================== 通用配置 ========================

# 数据库数量
databases 16

# 最大内存设置（根据实际情况调整）
# maxmemory 2gb

# 内存淘汰策略
# maxmemory-policy allkeys-lru

# 日志级别
loglevel notice

# 日志文件
logfile ""

# ======================== 网络配置 ========================

# 绑定地址
bind 127.0.0.1 192.168.252.131

# 端口
port 6379

# 密码认证
requirepass root

# 客户端超时时间（秒）
timeout 0

# TCP keepalive
tcp-keepalive 300

# ======================== 安全配置 ========================

# 禁用危险命令
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# ======================== 性能优化配置 ========================

# 客户端连接数限制
# maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# ======================== 监控配置 ========================

# 启用延迟监控
latency-monitor-threshold 100

# ======================== 集群配置（如果使用集群） ========================

# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-require-full-coverage yes

# ======================== 主从复制配置（如果使用主从） ========================

# slaveof <masterip> <masterport>
# masterauth <master-password>
# slave-serve-stale-data yes
# slave-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5
# repl-ping-slave-period 10
# repl-timeout 60
# repl-disable-tcp-nodelay no
# repl-backlog-size 1mb
# repl-backlog-ttl 3600

# ======================== 哨兵配置（如果使用哨兵） ========================

# sentinel monitor mymaster 127.0.0.1 6379 2
# sentinel auth-pass mymaster root
# sentinel down-after-milliseconds mymaster 30000
# sentinel parallel-syncs mymaster 1
# sentinel failover-timeout mymaster 180000
