package com.example.performance;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Redis内存优化器
 * 提供内存使用优化策略和工具
 */
public class RedisMemoryOptimizer {
    
    private final JedisPool jedisPool;
    private final Map<String, Object> optimizationResults = new ConcurrentHashMap<>();
    
    public RedisMemoryOptimizer(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }
    
    /**
     * 执行内存优化
     */
    public void performMemoryOptimization() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("🧹 开始Redis内存优化");
        System.out.println("=" .repeat(80));
        
        try {
            analyzeMemoryUsage();
            optimizeExpiredKeys();
            optimizeLargeKeys();
            optimizeDataStructures();
            optimizeKeyNaming();
            generateMemoryReport();
            
        } catch (Exception e) {
            System.err.println("❌ 内存优化过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析内存使用情况
     */
    private void analyzeMemoryUsage() {
        System.out.println("\n💾 内存使用情况分析");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("memory");
            Map<String, String> memoryInfo = parseInfo(info);
            
            long usedMemory = Long.parseLong(memoryInfo.get("used_memory"));
            long usedMemoryRss = Long.parseLong(memoryInfo.get("used_memory_rss"));
            long usedMemoryPeak = Long.parseLong(memoryInfo.get("used_memory_peak"));
            double fragmentationRatio = Double.parseDouble(memoryInfo.get("mem_fragmentation_ratio"));
            
            System.out.println("当前内存使用: " + formatBytes(usedMemory));
            System.out.println("RSS内存: " + formatBytes(usedMemoryRss));
            System.out.println("峰值内存: " + formatBytes(usedMemoryPeak));
            System.out.println("内存碎片率: " + String.format("%.2f", fragmentationRatio));
            
            // 内存使用分布
            analyzeMemoryDistribution(jedis);
            
            optimizationResults.put("memory_before", usedMemory);
            optimizationResults.put("fragmentation_before", fragmentationRatio);
            
        } catch (Exception e) {
            System.err.println("❌ 内存分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析内存使用分布
     */
    private void analyzeMemoryDistribution(Jedis jedis) {
        try {
            // 按数据类型统计内存使用
            Map<String, Long> typeMemory = new HashMap<>();
            Map<String, Integer> typeCount = new HashMap<>();
            
            // 使用SCAN命令避免阻塞
            String cursor = "0";
            ScanParams scanParams = new ScanParams().count(100);
            
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                
                for (String key : scanResult.getResult()) {
                    try {
                        String type = jedis.type(key);
                        long memory = estimateKeyMemory(jedis, key, type);
                        
                        typeMemory.put(type, typeMemory.getOrDefault(type, 0L) + memory);
                        typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
                    } catch (Exception e) {
                        // 忽略单个键的错误
                    }
                }
            } while (!"0".equals(cursor));
            
            System.out.println("\n内存使用分布:");
            typeMemory.entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .forEach(entry -> {
                        String type = entry.getKey();
                        long memory = entry.getValue();
                        int count = typeCount.getOrDefault(type, 0);
                        System.out.printf("  %s: %s (%d个键)%n", 
                                type, formatBytes(memory), count);
                    });
            
            optimizationResults.put("type_memory", typeMemory);
            optimizationResults.put("type_count", typeCount);
            
        } catch (Exception e) {
            System.err.println("❌ 内存分布分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 估算键的内存使用
     */
    private long estimateKeyMemory(Jedis jedis, String key, String type) {
        try {
            long baseMemory = key.length() + 64; // 键名 + 基础开销
            
            switch (type.toLowerCase()) {
                case "string":
                    return baseMemory + jedis.strlen(key);
                case "list":
                    return baseMemory + jedis.llen(key) * 64; // 估算
                case "set":
                    return baseMemory + jedis.scard(key) * 64;
                case "zset":
                    return baseMemory + jedis.zcard(key) * 96; // 包含score
                case "hash":
                    return baseMemory + jedis.hlen(key) * 128; // 键值对
                default:
                    return baseMemory;
            }
        } catch (Exception e) {
            return 64; // 默认估算
        }
    }
    
    /**
     * 优化过期键
     */
    private void optimizeExpiredKeys() {
        System.out.println("\n⏰ 过期键优化");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            List<String> expiredKeys = new ArrayList<>();
            List<String> noTtlKeys = new ArrayList<>();
            
            String cursor = "0";
            ScanParams scanParams = new ScanParams().count(100);
            
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                
                for (String key : scanResult.getResult()) {
                    try {
                        long ttl = jedis.ttl(key);
                        if (ttl == -1) { // 没有设置过期时间
                            noTtlKeys.add(key);
                        } else if (ttl == -2) { // 键已过期但未被删除
                            expiredKeys.add(key);
                        }
                    } catch (Exception e) {
                        // 忽略单个键的错误
                    }
                }
            } while (!"0".equals(cursor));
            
            System.out.println("发现 " + expiredKeys.size() + " 个已过期但未删除的键");
            System.out.println("发现 " + noTtlKeys.size() + " 个未设置过期时间的键");
            
            // 清理过期键
            if (!expiredKeys.isEmpty()) {
                System.out.println("清理过期键...");
                for (String key : expiredKeys) {
                    try {
                        jedis.del(key);
                    } catch (Exception e) {
                        // 忽略删除错误
                    }
                }
                System.out.println("✅ 清理了 " + expiredKeys.size() + " 个过期键");
            }
            
            // 建议为无TTL的键设置过期时间
            if (!noTtlKeys.isEmpty()) {
                System.out.println("⚠️  建议为以下类型的键设置合适的过期时间:");
                Map<String, Integer> prefixCount = new HashMap<>();
                for (String key : noTtlKeys.subList(0, Math.min(100, noTtlKeys.size()))) {
                    String prefix = extractPrefix(key);
                    prefixCount.put(prefix, prefixCount.getOrDefault(prefix, 0) + 1);
                }
                
                prefixCount.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(10)
                        .forEach(entry -> 
                                System.out.printf("  %s: %d个键%n", entry.getKey(), entry.getValue()));
            }
            
            optimizationResults.put("expired_keys_cleaned", expiredKeys.size());
            optimizationResults.put("no_ttl_keys", noTtlKeys.size());
            
        } catch (Exception e) {
            System.err.println("❌ 过期键优化失败: " + e.getMessage());
        }
    }
    
    /**
     * 优化大键
     */
    private void optimizeLargeKeys() {
        System.out.println("\n🔍 大键优化");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            List<LargeKeyInfo> largeKeys = findLargeKeys(jedis);
            
            if (largeKeys.isEmpty()) {
                System.out.println("✅ 未发现需要优化的大键");
                return;
            }
            
            System.out.println("发现 " + largeKeys.size() + " 个大键需要优化:");
            
            for (LargeKeyInfo keyInfo : largeKeys) {
                System.out.printf("  %s (%s): %s%n", 
                        keyInfo.key, keyInfo.type, formatBytes(keyInfo.size));
                
                // 提供优化建议
                provideLargeKeyOptimization(keyInfo);
            }
            
            optimizationResults.put("large_keys_found", largeKeys.size());
            
        } catch (Exception e) {
            System.err.println("❌ 大键优化失败: " + e.getMessage());
        }
    }
    
    /**
     * 查找大键
     */
    private List<LargeKeyInfo> findLargeKeys(Jedis jedis) {
        List<LargeKeyInfo> largeKeys = new ArrayList<>();
        
        String cursor = "0";
        ScanParams scanParams = new ScanParams().count(100);
        
        do {
            ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
            cursor = scanResult.getCursor();
            
            for (String key : scanResult.getResult()) {
                try {
                    String type = jedis.type(key);
                    long size = estimateKeyMemory(jedis, key, type);
                    
                    // 大于1MB的键认为是大键
                    if (size > 1024 * 1024) {
                        largeKeys.add(new LargeKeyInfo(key, type, size));
                    }
                } catch (Exception e) {
                    // 忽略单个键的错误
                }
            }
        } while (!"0".equals(cursor));
        
        // 按大小排序
        largeKeys.sort((a, b) -> Long.compare(b.size, a.size));
        
        return largeKeys;
    }
    
    /**
     * 提供大键优化建议
     */
    private void provideLargeKeyOptimization(LargeKeyInfo keyInfo) {
        switch (keyInfo.type.toLowerCase()) {
            case "string":
                System.out.println("    建议: 考虑压缩数据或拆分为多个键");
                break;
            case "list":
                System.out.println("    建议: 使用分页或拆分为多个小列表");
                break;
            case "set":
                System.out.println("    建议: 使用分片或考虑使用Bloom Filter");
                break;
            case "zset":
                System.out.println("    建议: 按分数范围分片或使用分页");
                break;
            case "hash":
                System.out.println("    建议: 拆分为多个小Hash或使用嵌套结构");
                break;
        }
    }
    
    /**
     * 优化数据结构
     */
    private void optimizeDataStructures() {
        System.out.println("\n🏗️  数据结构优化");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            Map<String, List<String>> optimizationSuggestions = new HashMap<>();
            
            String cursor = "0";
            ScanParams scanParams = new ScanParams().count(100);
            
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                
                for (String key : scanResult.getResult()) {
                    try {
                        String type = jedis.type(key);
                        List<String> suggestions = analyzeDataStructure(jedis, key, type);
                        
                        if (!suggestions.isEmpty()) {
                            optimizationSuggestions.put(key, suggestions);
                        }
                    } catch (Exception e) {
                        // 忽略单个键的错误
                    }
                }
            } while (!"0".equals(cursor));
            
            if (optimizationSuggestions.isEmpty()) {
                System.out.println("✅ 数据结构使用合理");
            } else {
                System.out.println("数据结构优化建议:");
                optimizationSuggestions.entrySet().stream()
                        .limit(10) // 只显示前10个
                        .forEach(entry -> {
                            System.out.println("  " + entry.getKey() + ":");
                            entry.getValue().forEach(suggestion -> 
                                    System.out.println("    - " + suggestion));
                        });
            }
            
            optimizationResults.put("structure_suggestions", optimizationSuggestions.size());
            
        } catch (Exception e) {
            System.err.println("❌ 数据结构优化失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析数据结构使用
     */
    private List<String> analyzeDataStructure(Jedis jedis, String key, String type) {
        List<String> suggestions = new ArrayList<>();
        
        try {
            switch (type.toLowerCase()) {
                case "string":
                    long strlen = jedis.strlen(key);
                    if (strlen > 10 * 1024 * 1024) { // 大于10MB
                        suggestions.add("字符串过大，考虑压缩或拆分");
                    }
                    break;
                    
                case "list":
                    long llen = jedis.llen(key);
                    if (llen > 10000) {
                        suggestions.add("列表元素过多，考虑分页或使用其他数据结构");
                    }
                    break;
                    
                case "set":
                    long scard = jedis.scard(key);
                    if (scard > 10000) {
                        suggestions.add("集合元素过多，考虑使用Bloom Filter或分片");
                    }
                    break;
                    
                case "zset":
                    long zcard = jedis.zcard(key);
                    if (zcard > 10000) {
                        suggestions.add("有序集合元素过多，考虑分片或分页");
                    }
                    break;
                    
                case "hash":
                    long hlen = jedis.hlen(key);
                    if (hlen > 1000) {
                        suggestions.add("Hash字段过多，考虑拆分为多个Hash");
                    }
                    break;
            }
        } catch (Exception e) {
            // 忽略分析错误
        }
        
        return suggestions;
    }
    
    /**
     * 优化键命名
     */
    private void optimizeKeyNaming() {
        System.out.println("\n🏷️  键命名优化");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            Map<String, Integer> prefixStats = new HashMap<>();
            List<String> longKeys = new ArrayList<>();
            
            String cursor = "0";
            ScanParams scanParams = new ScanParams().count(100);
            
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                
                for (String key : scanResult.getResult()) {
                    // 统计前缀
                    String prefix = extractPrefix(key);
                    prefixStats.put(prefix, prefixStats.getOrDefault(prefix, 0) + 1);
                    
                    // 检查键名长度
                    if (key.length() > 100) {
                        longKeys.add(key);
                    }
                }
            } while (!"0".equals(cursor));
            
            System.out.println("键前缀分布 (Top 10):");
            prefixStats.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(10)
                    .forEach(entry -> 
                            System.out.printf("  %s: %d个键%n", entry.getKey(), entry.getValue()));
            
            if (!longKeys.isEmpty()) {
                System.out.println("\n发现 " + longKeys.size() + " 个过长的键名 (>100字符):");
                longKeys.stream().limit(5).forEach(key -> 
                        System.out.println("  " + key.substring(0, Math.min(key.length(), 80)) + "..."));
                System.out.println("⚠️  建议缩短键名以节省内存");
            }
            
            optimizationResults.put("long_keys", longKeys.size());
            
        } catch (Exception e) {
            System.err.println("❌ 键命名优化失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成内存优化报告
     */
    private void generateMemoryReport() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("📊 内存优化报告");
        System.out.println("=" .repeat(80));
        
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("memory");
            Map<String, String> memoryInfo = parseInfo(info);
            
            long usedMemoryAfter = Long.parseLong(memoryInfo.get("used_memory"));
            Long usedMemoryBefore = (Long) optimizationResults.get("memory_before");
            
            if (usedMemoryBefore != null) {
                long memorySaved = usedMemoryBefore - usedMemoryAfter;
                System.out.println("优化前内存使用: " + formatBytes(usedMemoryBefore));
                System.out.println("优化后内存使用: " + formatBytes(usedMemoryAfter));
                
                if (memorySaved > 0) {
                    System.out.println("节省内存: " + formatBytes(memorySaved) + 
                            " (" + String.format("%.1f%%", 100.0 * memorySaved / usedMemoryBefore) + ")");
                } else {
                    System.out.println("内存使用变化: " + formatBytes(-memorySaved));
                }
            }
            
            // 优化统计
            Integer expiredKeysCleared = (Integer) optimizationResults.get("expired_keys_cleaned");
            Integer largeKeysFound = (Integer) optimizationResults.get("large_keys_found");
            Integer structureSuggestions = (Integer) optimizationResults.get("structure_suggestions");
            Integer longKeys = (Integer) optimizationResults.get("long_keys");
            
            System.out.println("\n优化统计:");
            System.out.println("  清理过期键: " + (expiredKeysCleared != null ? expiredKeysCleared : 0) + " 个");
            System.out.println("  发现大键: " + (largeKeysFound != null ? largeKeysFound : 0) + " 个");
            System.out.println("  数据结构建议: " + (structureSuggestions != null ? structureSuggestions : 0) + " 个");
            System.out.println("  过长键名: " + (longKeys != null ? longKeys : 0) + " 个");
            
        } catch (Exception e) {
            System.err.println("❌ 生成优化报告失败: " + e.getMessage());
        }
    }
    
    // 辅助方法
    private Map<String, String> parseInfo(String info) {
        Map<String, String> result = new HashMap<>();
        String[] lines = info.split("\r\n");
        
        for (String line : lines) {
            if (line.contains(":") && !line.startsWith("#")) {
                String[] parts = line.split(":", 2);
                if (parts.length == 2) {
                    result.put(parts[0], parts[1]);
                }
            }
        }
        
        return result;
    }
    
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + "B";
        if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024));
        return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
    }
    
    private String extractPrefix(String key) {
        int colonIndex = key.indexOf(':');
        return colonIndex > 0 ? key.substring(0, colonIndex) : key;
    }
    
    // 内部类
    private static class LargeKeyInfo {
        final String key;
        final String type;
        final long size;
        
        LargeKeyInfo(String key, String type, long size) {
            this.key = key;
            this.type = type;
            this.size = size;
        }
    }
}
