package com.example.lock;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 可重入Redis分布式锁
 * 支持同一线程多次获取同一把锁
 */
public class ReentrantRedisLock {
    
    private final JedisPool jedisPool;
    private final String lockKey;
    private final String threadId;
    private final int expireTime;
    
    // Lua脚本：可重入加锁
    private static final String LOCK_SCRIPT = 
        "local key = KEYS[1] " +
        "local threadId = ARGV[1] " +
        "local expireTime = ARGV[2] " +
        "local lockValue = redis.call('hget', key, 'threadId') " +
        "if lockValue == false then " +
        "    redis.call('hset', key, 'threadId', threadId) " +
        "    redis.call('hset', key, 'count', 1) " +
        "    redis.call('expire', key, expireTime) " +
        "    return 1 " +
        "elseif lockValue == threadId then " +
        "    local count = redis.call('hget', key, 'count') " +
        "    redis.call('hset', key, 'count', count + 1) " +
        "    redis.call('expire', key, expireTime) " +
        "    return 1 " +
        "else " +
        "    return 0 " +
        "end";
    
    // Lua脚本：可重入解锁
    private static final String UNLOCK_SCRIPT = 
        "local key = KEYS[1] " +
        "local threadId = ARGV[1] " +
        "local lockValue = redis.call('hget', key, 'threadId') " +
        "if lockValue == false then " +
        "    return 0 " +
        "elseif lockValue == threadId then " +
        "    local count = redis.call('hget', key, 'count') " +
        "    if tonumber(count) > 1 then " +
        "        redis.call('hset', key, 'count', count - 1) " +
        "        return 1 " +
        "    else " +
        "        redis.call('del', key) " +
        "        return 1 " +
        "    end " +
        "else " +
        "    return 0 " +
        "end";
    
    public ReentrantRedisLock(JedisPool jedisPool, String lockKey) {
        this(jedisPool, lockKey, 30);
    }
    
    public ReentrantRedisLock(JedisPool jedisPool, String lockKey, int expireTime) {
        this.jedisPool = jedisPool;
        this.lockKey = "reentrant_lock:" + lockKey;
        this.threadId = Thread.currentThread().getId() + ":" + UUID.randomUUID().toString();
        this.expireTime = expireTime;
    }
    
    /**
     * 尝试获取锁
     * @return 是否成功获取锁
     */
    public boolean tryLock() {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(LOCK_SCRIPT,
                    Collections.singletonList(lockKey),
                    java.util.Arrays.asList(threadId, String.valueOf(expireTime)));
            
            boolean acquired = Long.valueOf(1).equals(result);
            
            if (acquired) {
                int count = getReentrantCount();
                System.out.println("🔒 可重入锁获取成功: " + lockKey + " (重入次数: " + count + ")");
            } else {
                System.out.println("❌ 可重入锁获取失败: " + lockKey + " (被其他线程占用)");
            }
            
            return acquired;
        } catch (Exception e) {
            System.err.println("❌ 获取可重入锁异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 尝试获取锁（带超时）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取锁
     */
    public boolean tryLock(long timeout, TimeUnit unit) {
        long timeoutMillis = unit.toMillis(timeout);
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            if (tryLock()) {
                return true;
            }
            
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        System.out.println("⏰ 获取可重入锁超时: " + lockKey);
        return false;
    }
    
    /**
     * 释放锁
     * @return 是否成功释放锁
     */
    public boolean unlock() {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(UNLOCK_SCRIPT,
                    Collections.singletonList(lockKey),
                    Collections.singletonList(threadId));
            
            boolean released = Long.valueOf(1).equals(result);
            
            if (released) {
                int count = getReentrantCount();
                if (count > 0) {
                    System.out.println("🔓 可重入锁释放成功: " + lockKey + " (剩余重入次数: " + count + ")");
                } else {
                    System.out.println("🔓 可重入锁完全释放: " + lockKey);
                }
            } else {
                System.out.println("⚠️  可重入锁释放失败: " + lockKey + " (锁不存在或不属于当前线程)");
            }
            
            return released;
        } catch (Exception e) {
            System.err.println("❌ 释放可重入锁异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取重入次数
     * @return 重入次数，0表示未持有锁
     */
    public int getReentrantCount() {
        try (Jedis jedis = jedisPool.getResource()) {
            String lockThreadId = jedis.hget(lockKey, "threadId");
            if (threadId.equals(lockThreadId)) {
                String countStr = jedis.hget(lockKey, "count");
                return countStr != null ? Integer.parseInt(countStr) : 0;
            }
            return 0;
        } catch (Exception e) {
            System.err.println("❌ 获取重入次数异常: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 检查锁是否存在
     * @return 锁是否存在
     */
    public boolean isLocked() {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(lockKey);
        } catch (Exception e) {
            System.err.println("❌ 检查锁状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查当前线程是否持有锁
     * @return 是否持有锁
     */
    public boolean isHeldByCurrentThread() {
        try (Jedis jedis = jedisPool.getResource()) {
            String lockThreadId = jedis.hget(lockKey, "threadId");
            return threadId.equals(lockThreadId);
        } catch (Exception e) {
            System.err.println("❌ 检查锁持有状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取锁的剩余过期时间
     * @return 剩余过期时间（秒）
     */
    public long getTimeToLive() {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.ttl(lockKey);
        } catch (Exception e) {
            System.err.println("❌ 获取锁TTL异常: " + e.getMessage());
            return -2;
        }
    }
    
    /**
     * 获取锁信息
     * @return 锁信息字符串
     */
    public String getLockInfo() {
        try (Jedis jedis = jedisPool.getResource()) {
            String lockThreadId = jedis.hget(lockKey, "threadId");
            String countStr = jedis.hget(lockKey, "count");
            long ttl = jedis.ttl(lockKey);
            
            if (lockThreadId == null) {
                return "可重入锁不存在: " + lockKey;
            } else {
                int count = countStr != null ? Integer.parseInt(countStr) : 0;
                return String.format("可重入锁信息: key=%s, threadId=%s, count=%d, ttl=%d秒, 是否持有=%s",
                        lockKey, lockThreadId, count, ttl, threadId.equals(lockThreadId));
            }
        } catch (Exception e) {
            return "获取可重入锁信息失败: " + e.getMessage();
        }
    }
    
    // Getter方法
    public String getLockKey() {
        return lockKey;
    }
    
    public String getThreadId() {
        return threadId;
    }
    
    public int getExpireTime() {
        return expireTime;
    }
}
