package com.example.performance;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.Pipeline;

import java.util.*;
import java.util.concurrent.*;

/**
 * Redis网络优化器
 * 提供网络连接和传输优化策略
 */
public class RedisNetworkOptimizer {
    
    private final String host;
    private final int port;
    private final String password;
    private JedisPool jedisPool;
    
    public RedisNetworkOptimizer(String host, int port, String password) {
        this.host = host;
        this.port = port;
        this.password = password;
        initializeOptimalPool();
    }
    
    /**
     * 初始化优化的连接池
     */
    private void initializeOptimalPool() {
        JedisPoolConfig config = new JedisPoolConfig();
        
        // 连接池优化配置
        config.setMaxTotal(200);           // 最大连接数
        config.setMaxIdle(50);             // 最大空闲连接数
        config.setMinIdle(20);             // 最小空闲连接数
        config.setTestOnBorrow(true);      // 获取连接时测试
        config.setTestOnReturn(false);     // 归还连接时不测试
        config.setTestWhileIdle(true);     // 空闲时测试连接
        config.setMinEvictableIdleTimeMillis(60000); // 空闲连接最小生存时间
        config.setTimeBetweenEvictionRunsMillis(30000); // 空闲连接检查间隔
        config.setNumTestsPerEvictionRun(3); // 每次检查的连接数
        config.setBlockWhenExhausted(true); // 连接耗尽时阻塞
        config.setMaxWaitMillis(3000);     // 最大等待时间
        
        if (password != null && !password.isEmpty()) {
            this.jedisPool = new JedisPool(config, host, port, 3000, password);
        } else {
            this.jedisPool = new JedisPool(config, host, port, 3000);
        }
        
        System.out.println("🔧 优化的Redis连接池已初始化");
    }
    
    /**
     * 执行网络优化测试
     */
    public void performNetworkOptimization() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("🌐 开始Redis网络优化测试");
        System.out.println("=" .repeat(80));
        
        try {
            testConnectionLatency();
            testThroughput();
            testPipelinePerformance();
            testConnectionPoolPerformance();
            testBatchOperations();
            generateNetworkReport();
            
        } catch (Exception e) {
            System.err.println("❌ 网络优化测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试连接延迟
     */
    private void testConnectionLatency() {
        System.out.println("\n⏱️  连接延迟测试");
        System.out.println("-" .repeat(50));
        
        List<Long> latencies = new ArrayList<>();
        
        try (Jedis jedis = jedisPool.getResource()) {
            // 预热
            for (int i = 0; i < 10; i++) {
                jedis.ping();
            }
            
            // 测试延迟
            for (int i = 0; i < 100; i++) {
                long start = System.nanoTime();
                jedis.ping();
                long end = System.nanoTime();
                latencies.add((end - start) / 1000); // 转换为微秒
            }
            
            // 统计延迟
            Collections.sort(latencies);
            long min = latencies.get(0);
            long max = latencies.get(latencies.size() - 1);
            long avg = (long) latencies.stream().mapToLong(Long::longValue).average().orElse(0);
            long p95 = latencies.get((int) (latencies.size() * 0.95));
            long p99 = latencies.get((int) (latencies.size() * 0.99));
            
            System.out.println("延迟统计 (微秒):");
            System.out.println("  最小值: " + min + "μs");
            System.out.println("  最大值: " + max + "μs");
            System.out.println("  平均值: " + avg + "μs");
            System.out.println("  P95: " + p95 + "μs");
            System.out.println("  P99: " + p99 + "μs");
            
            if (avg > 1000) { // 大于1ms
                System.out.println("⚠️  平均延迟较高，建议检查网络连接");
            } else {
                System.out.println("✅ 网络延迟正常");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 延迟测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试吞吐量
     */
    private void testThroughput() {
        System.out.println("\n📊 吞吐量测试");
        System.out.println("-" .repeat(50));
        
        try {
            // 单连接吞吐量测试
            testSingleConnectionThroughput();
            
            // 多连接并发吞吐量测试
            testConcurrentThroughput();
            
        } catch (Exception e) {
            System.err.println("❌ 吞吐量测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试单连接吞吐量
     */
    private void testSingleConnectionThroughput() {
        System.out.println("单连接吞吐量测试:");
        
        try (Jedis jedis = jedisPool.getResource()) {
            int operations = 10000;
            
            // SET操作测试
            long start = System.currentTimeMillis();
            for (int i = 0; i < operations; i++) {
                jedis.set("test:throughput:" + i, "value" + i);
            }
            long setTime = System.currentTimeMillis() - start;
            
            // GET操作测试
            start = System.currentTimeMillis();
            for (int i = 0; i < operations; i++) {
                jedis.get("test:throughput:" + i);
            }
            long getTime = System.currentTimeMillis() - start;
            
            // 清理测试数据
            for (int i = 0; i < operations; i++) {
                jedis.del("test:throughput:" + i);
            }
            
            double setThroughput = operations * 1000.0 / setTime;
            double getThroughput = operations * 1000.0 / getTime;
            
            System.out.printf("  SET操作: %.0f ops/sec%n", setThroughput);
            System.out.printf("  GET操作: %.0f ops/sec%n", getThroughput);
            
        } catch (Exception e) {
            System.err.println("❌ 单连接吞吐量测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试并发吞吐量
     */
    private void testConcurrentThroughput() {
        System.out.println("\n并发吞吐量测试:");
        
        int threadCount = 10;
        int operationsPerThread = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long start = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try (Jedis jedis = jedisPool.getResource()) {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String key = "test:concurrent:" + threadId + ":" + j;
                        jedis.set(key, "value" + j);
                        jedis.get(key);
                        jedis.del(key);
                    }
                } catch (Exception e) {
                    System.err.println("线程" + threadId + "执行失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
            long totalTime = System.currentTimeMillis() - start;
            int totalOperations = threadCount * operationsPerThread * 3; // SET + GET + DEL
            double concurrentThroughput = totalOperations * 1000.0 / totalTime;
            
            System.out.printf("  并发操作: %.0f ops/sec (%d线程)%n", concurrentThroughput, threadCount);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
    }
    
    /**
     * 测试Pipeline性能
     */
    private void testPipelinePerformance() {
        System.out.println("\n🚀 Pipeline性能测试");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            int operations = 10000;
            
            // 普通操作测试
            long start = System.currentTimeMillis();
            for (int i = 0; i < operations; i++) {
                jedis.set("test:normal:" + i, "value" + i);
            }
            long normalTime = System.currentTimeMillis() - start;
            
            // Pipeline操作测试
            start = System.currentTimeMillis();
            Pipeline pipeline = jedis.pipelined();
            for (int i = 0; i < operations; i++) {
                pipeline.set("test:pipeline:" + i, "value" + i);
            }
            pipeline.sync();
            long pipelineTime = System.currentTimeMillis() - start;
            
            // 清理测试数据
            pipeline = jedis.pipelined();
            for (int i = 0; i < operations; i++) {
                pipeline.del("test:normal:" + i);
                pipeline.del("test:pipeline:" + i);
            }
            pipeline.sync();
            
            double normalThroughput = operations * 1000.0 / normalTime;
            double pipelineThroughput = operations * 1000.0 / pipelineTime;
            double improvement = pipelineThroughput / normalThroughput;
            
            System.out.printf("普通操作: %.0f ops/sec%n", normalThroughput);
            System.out.printf("Pipeline操作: %.0f ops/sec%n", pipelineThroughput);
            System.out.printf("性能提升: %.1fx%n", improvement);
            
            if (improvement > 5) {
                System.out.println("✅ Pipeline显著提升性能，建议在批量操作中使用");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Pipeline性能测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试连接池性能
     */
    private void testConnectionPoolPerformance() {
        System.out.println("\n🏊 连接池性能测试");
        System.out.println("-" .repeat(50));
        
        // 测试不同连接池配置的性能
        testPoolConfiguration("当前配置", jedisPool);
        
        // 测试小连接池配置
        JedisPoolConfig smallConfig = new JedisPoolConfig();
        smallConfig.setMaxTotal(10);
        smallConfig.setMaxIdle(5);
        smallConfig.setMinIdle(2);
        
        JedisPool smallPool = password != null && !password.isEmpty() ?
                new JedisPool(smallConfig, host, port, 3000, password) :
                new JedisPool(smallConfig, host, port, 3000);
        
        testPoolConfiguration("小连接池", smallPool);
        smallPool.close();
        
        // 测试大连接池配置
        JedisPoolConfig largeConfig = new JedisPoolConfig();
        largeConfig.setMaxTotal(500);
        largeConfig.setMaxIdle(100);
        largeConfig.setMinIdle(50);
        
        JedisPool largePool = password != null && !password.isEmpty() ?
                new JedisPool(largeConfig, host, port, 3000, password) :
                new JedisPool(largeConfig, host, port, 3000);
        
        testPoolConfiguration("大连接池", largePool);
        largePool.close();
    }
    
    /**
     * 测试特定连接池配置的性能
     */
    private void testPoolConfiguration(String configName, JedisPool pool) {
        System.out.println(configName + "性能:");
        
        int threadCount = 20;
        int operationsPerThread = 500;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long start = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        try (Jedis jedis = pool.getResource()) {
                            jedis.set("test:pool:" + threadId + ":" + j, "value");
                            jedis.get("test:pool:" + threadId + ":" + j);
                            jedis.del("test:pool:" + threadId + ":" + j);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("线程" + threadId + "执行失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
            long totalTime = System.currentTimeMillis() - start;
            int totalOperations = threadCount * operationsPerThread * 3;
            double throughput = totalOperations * 1000.0 / totalTime;
            
            System.out.printf("  吞吐量: %.0f ops/sec%n", throughput);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
    }
    
    /**
     * 测试批量操作
     */
    private void testBatchOperations() {
        System.out.println("\n📦 批量操作测试");
        System.out.println("-" .repeat(50));
        
        try (Jedis jedis = jedisPool.getResource()) {
            int batchSize = 1000;
            
            // 测试MSET vs 多个SET
            testMSetVsMultipleSet(jedis, batchSize);
            
            // 测试MGET vs 多个GET
            testMGetVsMultipleGet(jedis, batchSize);
            
        } catch (Exception e) {
            System.err.println("❌ 批量操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试MSET vs 多个SET
     */
    private void testMSetVsMultipleSet(Jedis jedis, int count) {
        System.out.println("MSET vs 多个SET测试:");
        
        // 多个SET操作
        long start = System.currentTimeMillis();
        for (int i = 0; i < count; i++) {
            jedis.set("test:mset:" + i, "value" + i);
        }
        long multipleSetTime = System.currentTimeMillis() - start;
        
        // 清理
        for (int i = 0; i < count; i++) {
            jedis.del("test:mset:" + i);
        }
        
        // MSET操作
        String[] keyValues = new String[count * 2];
        for (int i = 0; i < count; i++) {
            keyValues[i * 2] = "test:mset:" + i;
            keyValues[i * 2 + 1] = "value" + i;
        }
        
        start = System.currentTimeMillis();
        jedis.mset(keyValues);
        long msetTime = System.currentTimeMillis() - start;
        
        // 清理
        String[] keys = new String[count];
        for (int i = 0; i < count; i++) {
            keys[i] = "test:mset:" + i;
        }
        jedis.del(keys);
        
        double multipleSetThroughput = count * 1000.0 / multipleSetTime;
        double msetThroughput = count * 1000.0 / msetTime;
        double improvement = msetThroughput / multipleSetThroughput;
        
        System.out.printf("  多个SET: %.0f ops/sec%n", multipleSetThroughput);
        System.out.printf("  MSET: %.0f ops/sec%n", msetThroughput);
        System.out.printf("  性能提升: %.1fx%n", improvement);
    }
    
    /**
     * 测试MGET vs 多个GET
     */
    private void testMGetVsMultipleGet(Jedis jedis, int count) {
        System.out.println("\nMGET vs 多个GET测试:");
        
        // 准备测试数据
        String[] keyValues = new String[count * 2];
        String[] keys = new String[count];
        for (int i = 0; i < count; i++) {
            keys[i] = "test:mget:" + i;
            keyValues[i * 2] = keys[i];
            keyValues[i * 2 + 1] = "value" + i;
        }
        jedis.mset(keyValues);
        
        // 多个GET操作
        long start = System.currentTimeMillis();
        for (String key : keys) {
            jedis.get(key);
        }
        long multipleGetTime = System.currentTimeMillis() - start;
        
        // MGET操作
        start = System.currentTimeMillis();
        jedis.mget(keys);
        long mgetTime = System.currentTimeMillis() - start;
        
        // 清理
        jedis.del(keys);
        
        double multipleGetThroughput = count * 1000.0 / multipleGetTime;
        double mgetThroughput = count * 1000.0 / mgetTime;
        double improvement = mgetThroughput / multipleGetThroughput;
        
        System.out.printf("  多个GET: %.0f ops/sec%n", multipleGetThroughput);
        System.out.printf("  MGET: %.0f ops/sec%n", mgetThroughput);
        System.out.printf("  性能提升: %.1fx%n", improvement);
    }
    
    /**
     * 生成网络优化报告
     */
    private void generateNetworkReport() {
        System.out.println("\n" + "=" .repeat(80));
        System.out.println("📋 网络优化建议报告");
        System.out.println("=" .repeat(80));
        
        List<String> recommendations = new ArrayList<>();
        
        recommendations.add("使用连接池管理连接，避免频繁创建和销毁连接");
        recommendations.add("在批量操作中使用Pipeline或批量命令(MGET/MSET)");
        recommendations.add("合理配置连接池参数，平衡资源使用和性能");
        recommendations.add("对于高延迟网络，增加连接超时时间");
        recommendations.add("使用Redis Cluster或读写分离减少单点压力");
        recommendations.add("考虑使用Redis的压缩功能减少网络传输");
        recommendations.add("监控网络指标，及时发现和解决网络问题");
        
        System.out.println("网络优化建议:");
        for (int i = 0; i < recommendations.size(); i++) {
            System.out.println((i + 1) + ". " + recommendations.get(i));
        }
        
        System.out.println("\n连接池配置建议:");
        System.out.println("- MaxTotal: 根据并发量设置，一般为并发数的1.5-2倍");
        System.out.println("- MaxIdle: 设置为MaxTotal的50%-80%");
        System.out.println("- MinIdle: 设置为MaxTotal的10%-20%");
        System.out.println("- TestOnBorrow: 生产环境建议设置为true");
        System.out.println("- MaxWaitMillis: 根据业务容忍度设置，一般3-5秒");
    }
    
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
    }
    
    public JedisPool getJedisPool() {
        return jedisPool;
    }
}
