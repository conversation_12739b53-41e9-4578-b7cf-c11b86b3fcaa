package com.zhentao.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis持久化配置服务
 * 负责管理RDB和AOF持久化机制
 */
@Service
public class RedisPersistenceService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private JedisPool jedisPool;

    @PostConstruct
    public void init() {
        // 初始化Jedis连接池
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(20);
        config.setMaxIdle(10);
        config.setMinIdle(5);
        
        jedisPool = new JedisPool(config, "192.168.252.131", 6379, 3000, "root");
    }

    /**
     * 配置RDB持久化
     * @param saveSeconds RDB保存间隔时间（秒）
     * @param changedKeys 变更的key数量阈值
     * @return 配置结果
     */
    public String configureRDB(int saveSeconds, int changedKeys) {
        try (Jedis jedis = jedisPool.getResource()) {
            // 设置RDB保存策略
            String saveConfig = String.format("save %d %d", saveSeconds, changedKeys);
            String result = jedis.configSet("save", String.format("%d %d", saveSeconds, changedKeys));
            
            return String.format("RDB配置成功: %s, 结果: %s", saveConfig, result);
        } catch (Exception e) {
            return "RDB配置失败: " + e.getMessage();
        }
    }

    /**
     * 配置AOF持久化
     * @param enable 是否启用AOF
     * @param fsync AOF同步策略 (always/everysec/no)
     * @return 配置结果
     */
    public String configureAOF(boolean enable, String fsync) {
        try (Jedis jedis = jedisPool.getResource()) {
            StringBuilder result = new StringBuilder();
            
            // 启用/禁用AOF
            String aofResult = jedis.configSet("appendonly", enable ? "yes" : "no");
            result.append("AOF启用状态: ").append(aofResult).append("; ");
            
            // 设置AOF同步策略
            if (enable && fsync != null) {
                String fsyncResult = jedis.configSet("appendfsync", fsync);
                result.append("AOF同步策略: ").append(fsyncResult);
            }
            
            return result.toString();
        } catch (Exception e) {
            return "AOF配置失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发RDB保存
     * @param async 是否异步保存
     * @return 保存结果
     */
    public String triggerRDBSave(boolean async) {
        try (Jedis jedis = jedisPool.getResource()) {
            if (async) {
                // 异步保存（后台保存）
                String result = jedis.bgsave();
                return "异步RDB保存已触发: " + result;
            } else {
                // 同步保存（阻塞保存）
                String result = jedis.save();
                return "同步RDB保存完成: " + result;
            }
        } catch (Exception e) {
            return "RDB保存失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发AOF重写
     * @return 重写结果
     */
    public String triggerAOFRewrite() {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.bgrewriteaof();
            return "AOF重写已触发: " + result;
        } catch (Exception e) {
            return "AOF重写失败: " + e.getMessage();
        }
    }

    /**
     * 获取持久化状态信息
     * @return 状态信息
     */
    public Map<String, String> getPersistenceStatus() {
        Map<String, String> status = new HashMap<>();
        
        try (Jedis jedis = jedisPool.getResource()) {
            // 获取RDB相关信息
            List<String> rdbInfo = jedis.configGet("save");
            if (rdbInfo.size() >= 2) {
                status.put("rdb_save_config", rdbInfo.get(1));
            }
            
            // 获取AOF相关信息
            List<String> aofEnabled = jedis.configGet("appendonly");
            if (aofEnabled.size() >= 2) {
                status.put("aof_enabled", aofEnabled.get(1));
            }
            
            List<String> aofFsync = jedis.configGet("appendfsync");
            if (aofFsync.size() >= 2) {
                status.put("aof_fsync", aofFsync.get(1));
            }
            
            // 获取最后保存时间
            String info = jedis.info("persistence");
            status.put("persistence_info", info);
            
        } catch (Exception e) {
            status.put("error", "获取状态失败: " + e.getMessage());
        }
        
        return status;
    }

    /**
     * 获取详细的持久化统计信息
     * @return 统计信息
     */
    public String getPersistenceStats() {
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info("persistence");
            
            StringBuilder stats = new StringBuilder();
            stats.append("=== Redis持久化统计信息 ===\n");
            
            String[] lines = info.split("\r\n");
            for (String line : lines) {
                if (line.contains(":") && !line.startsWith("#")) {
                    String[] parts = line.split(":");
                    if (parts.length == 2) {
                        String key = parts[0];
                        String value = parts[1];
                        
                        // 格式化重要的持久化指标
                        switch (key) {
                            case "rdb_changes_since_last_save":
                                stats.append("RDB上次保存后变更数: ").append(value).append("\n");
                                break;
                            case "rdb_bgsave_in_progress":
                                stats.append("RDB后台保存进行中: ").append("1".equals(value) ? "是" : "否").append("\n");
                                break;
                            case "rdb_last_save_time":
                                stats.append("RDB最后保存时间: ").append(value).append("\n");
                                break;
                            case "rdb_last_bgsave_status":
                                stats.append("RDB最后保存状态: ").append(value).append("\n");
                                break;
                            case "aof_enabled":
                                stats.append("AOF启用状态: ").append("1".equals(value) ? "启用" : "禁用").append("\n");
                                break;
                            case "aof_rewrite_in_progress":
                                stats.append("AOF重写进行中: ").append("1".equals(value) ? "是" : "否").append("\n");
                                break;
                            case "aof_last_rewrite_time_sec":
                                stats.append("AOF最后重写耗时: ").append(value).append("秒\n");
                                break;
                            case "aof_current_size":
                                stats.append("AOF当前大小: ").append(value).append("字节\n");
                                break;
                            case "aof_base_size":
                                stats.append("AOF基础大小: ").append(value).append("字节\n");
                                break;
                        }
                    }
                }
            }
            
            return stats.toString();
        } catch (Exception e) {
            return "获取持久化统计失败: " + e.getMessage();
        }
    }

    /**
     * 设置RDB压缩
     * @param enable 是否启用压缩
     * @return 设置结果
     */
    public String setRDBCompression(boolean enable) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.configSet("rdbcompression", enable ? "yes" : "no");
            return "RDB压缩设置: " + (enable ? "启用" : "禁用") + ", 结果: " + result;
        } catch (Exception e) {
            return "设置RDB压缩失败: " + e.getMessage();
        }
    }

    /**
     * 设置RDB校验和
     * @param enable 是否启用校验和
     * @return 设置结果
     */
    public String setRDBChecksum(boolean enable) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.configSet("rdbchecksum", enable ? "yes" : "no");
            return "RDB校验和设置: " + (enable ? "启用" : "禁用") + ", 结果: " + result;
        } catch (Exception e) {
            return "设置RDB校验和失败: " + e.getMessage();
        }
    }

    /**
     * 设置AOF重写配置
     * @param minSize AOF重写最小文件大小（MB）
     * @param percentage AOF重写百分比阈值
     * @return 设置结果
     */
    public String setAOFRewriteConfig(int minSize, int percentage) {
        try (Jedis jedis = jedisPool.getResource()) {
            StringBuilder result = new StringBuilder();
            
            // 设置最小重写大小
            String minSizeResult = jedis.configSet("auto-aof-rewrite-min-size", minSize + "mb");
            result.append("AOF重写最小大小: ").append(minSizeResult).append("; ");
            
            // 设置重写百分比
            String percentageResult = jedis.configSet("auto-aof-rewrite-percentage", String.valueOf(percentage));
            result.append("AOF重写百分比: ").append(percentageResult);
            
            return result.toString();
        } catch (Exception e) {
            return "设置AOF重写配置失败: " + e.getMessage();
        }
    }

    /**
     * 获取当前Redis配置
     * @param pattern 配置项模式
     * @return 配置信息
     */
    public Map<String, String> getRedisConfig(String pattern) {
        Map<String, String> config = new HashMap<>();
        
        try (Jedis jedis = jedisPool.getResource()) {
            List<String> configList = jedis.configGet(pattern);
            
            for (int i = 0; i < configList.size(); i += 2) {
                if (i + 1 < configList.size()) {
                    config.put(configList.get(i), configList.get(i + 1));
                }
            }
        } catch (Exception e) {
            config.put("error", "获取配置失败: " + e.getMessage());
        }
        
        return config;
    }
}
