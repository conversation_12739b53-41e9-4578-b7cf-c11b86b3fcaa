package com.zhentao.service;

import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis持久化配置服务
 * 负责管理RDB和AOF持久化机制
 */
@Service
public class RedisPersistenceService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private JedisPool jedisPool;

    @PostConstruct
    public void init() {
        // 初始化Jedis连接池
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(20);
        config.setMaxIdle(10);
        config.setMinIdle(5);
        
        jedisPool = new JedisPool(config, "192.168.252.131", 6379, 3000, "root");
    }

    /**
     * 配置RDB持久化
     * @param saveSeconds RDB保存间隔时间（秒）
     * @param changedKeys 变更的key数量阈值
     * @return 配置结果
     */
    public String configureRDB(int saveSeconds, int changedKeys) {
        try (Jedis jedis = jedisPool.getResource()) {
            // 设置RDB保存策略
            String saveConfig = String.format("save %d %d", saveSeconds, changedKeys);
            String result = jedis.configSet("save", String.format("%d %d", saveSeconds, changedKeys));
            
            return String.format("RDB配置成功: %s, 结果: %s", saveConfig, result);
        } catch (Exception e) {
            return "RDB配置失败: " + e.getMessage();
        }
    }

    /**
     * 配置AOF持久化
     * @param enable 是否启用AOF
     * @param fsync AOF同步策略 (always/everysec/no)
     * @return 配置结果
     */
    public String configureAOF(boolean enable, String fsync) {
        try (Jedis jedis = jedisPool.getResource()) {
            StringBuilder result = new StringBuilder();

            // 启用/禁用AOF
            String aofResult = jedis.configSet("appendonly", enable ? "yes" : "no");
            result.append("AOF启用状态: ").append(aofResult).append("; ");
            
            // 设置AOF同步策略
            if (enable && fsync != null) {
                String fsyncResult = jedis.configSet("appendfsync", fsync);
                result.append("AOF同步策略: ").append(fsyncResult);
            }
            
            return result.toString();
        } catch (Exception e) {
            return "AOF配置失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发RDB保存
     * @param async 是否异步保存
     * @return 保存结果
     */
    public String triggerRDBSave(boolean async) {
        try (Jedis jedis = jedisPool.getResource()) {
            if (async) {
                // 异步保存（后台保存）
                String result = jedis.bgsave();
                return "异步RDB保存已触发: " + result;
            } else {
                // 同步保存（阻塞保存）
                String result = jedis.save();
                return "同步RDB保存完成: " + result;
            }
        } catch (Exception e) {
            return "RDB保存失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发AOF重写
     * @return 重写结果
     */
    public String triggerAOFRewrite() {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.bgrewriteaof();
            return "AOF重写已触发: " + result;
        } catch (Exception e) {
            return "AOF重写失败: " + e.getMessage();
        }
    }





    public Map<String, String> getRedisConfig(String pattern) {
        Map<String, String> config = new HashMap<>();
        
        try (Jedis jedis = jedisPool.getResource()) {
            List<String> configList = jedis.configGet(pattern);
            
            for (int i = 0; i < configList.size(); i += 2) {
                if (i + 1 < configList.size()) {
                    config.put(configList.get(i), configList.get(i + 1));
                }
            }
        } catch (Exception e) {
            config.put("error", "获取配置失败: " + e.getMessage());
        }
        
        return config;
    }

//    @RequestMapping("/rdb/config")
//    public String getRDBConfig() {
//        var config = persistenceService.getRedisConfig("*save*");
//        config.putAll(persistenceService.getRedisConfig("*rdb*"));
//
//        StringBuilder result = new StringBuilder();
//        result.append("=== RDB持久化配置 ===\n");
//
//        config.forEach((key, value) -> {
//            result.append(key).append(": ").append(value).append("\n");
//        });
//        return result.toString();
//    }
//
//
//    @RequestMapping("/aof/config")
//    public String getAOFConfig() {
//        var config = persistenceService.getRedisConfig("*aof*");
//        config.putAll(persistenceService.getRedisConfig("*append*"));
//
//        StringBuilder result = new StringBuilder();
//        result.append("=== AOF持久化配置 ===\n");
//
//        config.forEach((key, value) -> {
//            result.append(key).append(": ").append(value).append("\n");
//        });
//
//        return result.toString();
//    }
}
