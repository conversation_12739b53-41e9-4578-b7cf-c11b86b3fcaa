package com.zhentao.service;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;

/**
 * 缓存击穿防护服务
 * 使用布隆过滤器 + 互斥锁的组合策略防止缓存击穿
 */
@Service
public class CacheBreakdownProtectionService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 布隆过滤器 - 快速判断数据是否可能存在
    private BloomFilter<String> bloomFilter;

    // 互斥锁映射 - 防止并发重建缓存
    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    // 统计信息
    private volatile long bloomFilterHits = 0;
    private volatile long bloomFilterMisses = 0;
    private volatile long cacheHits = 0;
    private volatile long cacheMisses = 0;
    private volatile long lockWaits = 0;
    private volatile long lockTimeouts = 0;

    @PostConstruct
    public void init() {
        // 初始化布隆过滤器
        long expectedInsertions = 1000000L;
        double fpp = 0.01; // 1% 误判率
        
        bloomFilter = BloomFilter.create(
                Funnels.stringFunnel(StandardCharsets.UTF_8),
                expectedInsertions,
                fpp
        );
    }

    /**
     * 获取缓存数据，带缓存击穿防护
     * @param key 缓存key
     * @param dataLoader 数据加载函数（从数据库查询）
     * @param expireSeconds 缓存过期时间（秒）
     * @return 数据
     */
    public String getWithBreakdownProtection(String key, Function<String, String> dataLoader, int expireSeconds) {
        // 1. 布隆过滤器检查
        if (!bloomFilter.mightContain(key)) {
            bloomFilterMisses++;
            return null; // 数据一定不存在
        }
        bloomFilterHits++;

        // 2. 查询Redis缓存
        String cacheValue = redisTemplate.opsForValue().get(key);
        if (cacheValue != null) {
            if ("NULL".equals(cacheValue)) {
                return null; // 空值缓存
            }
            cacheHits++;
            return cacheValue;
        }
        cacheMisses++;

        // 3. 缓存未命中，使用互斥锁防止击穿
        return rebuildCacheWithLock(key, dataLoader, expireSeconds);
    }

    /**
     * 使用互斥锁重建缓存
     */
    private String rebuildCacheWithLock(String key, Function<String, String> dataLoader, int expireSeconds) {
        ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());

        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    // 双重检查缓存
                    String cacheValue = redisTemplate.opsForValue().get(key);
                    if (cacheValue != null) {
                        return "NULL".equals(cacheValue) ? null : cacheValue;
                    }

                    // 从数据源加载数据
                    String dbValue = dataLoader.apply(key);
                    
                    if (dbValue != null) {
                        // 缓存真实数据
                        redisTemplate.opsForValue().set(key, dbValue, expireSeconds, TimeUnit.SECONDS);
                        // 添加到布隆过滤器
                        bloomFilter.put(key);
                        return dbValue;
                    } else {
                        // 缓存空值，防止频繁查询
                        redisTemplate.opsForValue().set(key, "NULL", 60, TimeUnit.SECONDS);
                        return null;
                    }
                } finally {
                    lock.unlock();
                    lockMap.remove(key);
                }
            } else {
                lockTimeouts++;
                // 锁超时，返回null或降级数据
                return null;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            lockWaits++;
            return null;
        }
    }

    /**
     * 预加载数据到布隆过滤器
     * @param key 数据key
     */
    public void addToBloomFilter(String key) {
        bloomFilter.put(key);
    }

    /**
     * 批量预加载数据到布隆过滤器
     * @param keys 数据key数组
     */
    public void batchAddToBloomFilter(String[] keys) {
        for (String key : keys) {
            if (key != null && !key.trim().isEmpty()) {
                bloomFilter.put(key.trim());
            }
        }
    }

    /**
     * 检查key是否可能存在于布隆过滤器中
     * @param key 要检查的key
     * @return true表示可能存在，false表示一定不存在
     */
    public boolean mightContain(String key) {
        return bloomFilter.mightContain(key);
    }

    /**
     * 强制删除缓存
     * @param key 缓存key
     */
    public void evictCache(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 获取统计信息
     * @return 统计信息字符串
     */
    public String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== 缓存击穿防护统计 ===\n");
        stats.append("布隆过滤器命中: ").append(bloomFilterHits).append("\n");
        stats.append("布隆过滤器拦截: ").append(bloomFilterMisses).append("\n");
        stats.append("缓存命中: ").append(cacheHits).append("\n");
        stats.append("缓存未命中: ").append(cacheMisses).append("\n");
        stats.append("锁等待次数: ").append(lockWaits).append("\n");
        stats.append("锁超时次数: ").append(lockTimeouts).append("\n");
        stats.append("当前活跃锁: ").append(lockMap.size()).append("\n");
        
        // 计算命中率
        long totalBloomRequests = bloomFilterHits + bloomFilterMisses;
        long totalCacheRequests = cacheHits + cacheMisses;
        
        if (totalBloomRequests > 0) {
            double bloomHitRate = (double) bloomFilterHits / totalBloomRequests * 100;
            stats.append("布隆过滤器命中率: ").append(String.format("%.2f%%", bloomHitRate)).append("\n");
        }
        
        if (totalCacheRequests > 0) {
            double cacheHitRate = (double) cacheHits / totalCacheRequests * 100;
            stats.append("缓存命中率: ").append(String.format("%.2f%%", cacheHitRate)).append("\n");
        }
        
        return stats.toString();
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        bloomFilterHits = 0;
        bloomFilterMisses = 0;
        cacheHits = 0;
        cacheMisses = 0;
        lockWaits = 0;
        lockTimeouts = 0;
    }

    /**
     * 清理无效锁
     * @return 清理的锁数量
     */
    public int cleanupLocks() {
        int beforeSize = lockMap.size();
        lockMap.entrySet().removeIf(entry -> !entry.getValue().isLocked());
        return beforeSize - lockMap.size();
    }

    /**
     * 获取当前锁的数量
     * @return 锁数量
     */
    public int getLockCount() {
        return lockMap.size();
    }
}
