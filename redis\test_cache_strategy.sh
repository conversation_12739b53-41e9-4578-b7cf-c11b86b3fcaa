#!/bin/bash

# Redis缓存策略测试脚本
# 确保Spring Boot应用已启动在localhost:8080

BASE_URL="http://localhost:8080"

echo "=== Redis缓存策略防护机制测试 ==="
echo

# 1. 测试缓存预热
echo "1. 测试缓存预热..."
curl -s "${BASE_URL}/cache/warmup?keys=user:1,user:2,user:3,product:1,product:2" 
echo -e "\n"

# 2. 测试缓存穿透防护
echo "2. 测试缓存穿透防护..."
echo "2.1 查询存在的数据："
curl -s "${BASE_URL}/cache/penetration-protection?key=user:1"
echo -e "\n"

echo "2.2 查询不存在的数据（布隆过滤器拦截）："
curl -s "${BASE_URL}/cache/penetration-protection?key=user:999999"
echo -e "\n"

# 3. 测试缓存击穿防护
echo "3. 测试缓存击穿防护..."
curl -s "${BASE_URL}/cache/breakdown-protection?key=user:100"
echo -e "\n"

# 4. 测试缓存雪崩防护
echo "4. 测试缓存雪崩防护..."
curl -s "${BASE_URL}/cache/avalanche-protection?key=product:50"
echo -e "\n"

# 5. 测试综合防护策略
echo "5. 测试综合防护策略..."
curl -s "${BASE_URL}/cache/comprehensive-protection?key=user:200"
echo -e "\n"

# 6. 测试降级模式
echo "6. 测试降级模式..."
echo "6.1 开启降级模式："
curl -s "${BASE_URL}/cache/degrade?enable=true"
echo -e "\n"

echo "6.2 降级模式下查询："
curl -s "${BASE_URL}/cache/comprehensive-protection?key=user:300"
echo -e "\n"

echo "6.3 关闭降级模式："
curl -s "${BASE_URL}/cache/degrade?enable=false"
echo -e "\n"

# 7. 查看缓存统计
echo "7. 查看缓存统计信息..."
curl -s "${BASE_URL}/cache/stats"
echo -e "\n"

# 8. 清理锁
echo "8. 清理过期锁..."
curl -s "${BASE_URL}/cache/cleanup"
echo -e "\n"

echo "=== 测试完成 ==="
