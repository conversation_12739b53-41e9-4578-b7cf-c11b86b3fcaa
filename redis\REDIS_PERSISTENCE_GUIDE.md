# Redis持久化配置查看指南

## 概述

本指南详细说明了如何查看和管理Redis的RDB和AOF持久化配置。通过提供的API接口，您可以实时查看Redis的持久化状态、配置参数和统计信息。

## 查看接口列表

### 1. 基础查看接口

#### 查看持久化状态
```
GET /redis/status
```
**功能**: 查看Redis持久化的基本状态信息
**返回**: RDB和AOF的启用状态、配置参数等

**示例**:
```bash
curl "http://localhost:1000/redis/status"
```

#### 查看详细统计信息
```
GET /redis/stats
```
**功能**: 查看详细的持久化统计信息
**返回**: 包括最后保存时间、文件大小、重写状态等详细信息

**示例**:
```bash
curl "http://localhost:1000/redis/stats"
```

### 2. 专项配置查看

#### 查看RDB配置
```
GET /redis/rdb/config
```
**功能**: 专门查看RDB相关的所有配置
**返回**: save策略、压缩设置、校验和等RDB配置

**示例**:
```bash
curl "http://localhost:1000/redis/rdb/config"
```

#### 查看AOF配置
```
GET /redis/aof/config
```
**功能**: 专门查看AOF相关的所有配置
**返回**: AOF启用状态、同步策略、重写配置等

**示例**:
```bash
curl "http://localhost:1000/redis/aof/config"
```

### 3. 完整配置查看

#### 查看所有持久化配置
```
GET /redis/persistence/all
```
**功能**: 一次性查看所有持久化相关的配置和状态
**返回**: RDB配置 + AOF配置 + 持久化状态的完整信息

**示例**:
```bash
curl "http://localhost:1000/redis/persistence/all"
```

### 4. 模式匹配查看

#### 通用配置查看
```
GET /redis/config?pattern={模式}
```
**功能**: 根据模式匹配查看特定的Redis配置
**参数**: 
- `pattern`: 配置项匹配模式（支持通配符*）

**常用模式**:
- `*save*`: 查看所有save相关配置
- `*aof*`: 查看所有AOF相关配置
- `*rdb*`: 查看所有RDB相关配置
- `*append*`: 查看所有append相关配置

**示例**:
```bash
# 查看save相关配置
curl "http://localhost:1000/redis/config?pattern=*save*"

# 查看AOF相关配置
curl "http://localhost:1000/redis/config?pattern=*aof*"

# 查看所有配置
curl "http://localhost:1000/redis/config?pattern=*"
```

## 配置和操作接口

### 1. RDB配置

#### 配置RDB保存策略
```
GET /RDB?saveSeconds={秒数}&changedKeys={变化数量}
```
**功能**: 设置RDB自动保存策略
**参数**:
- `saveSeconds`: 时间间隔（秒）
- `changedKeys`: key变化数量阈值

**示例**:
```bash
# 900秒内至少1个key变化时保存
curl "http://localhost:1000/RDB?saveSeconds=900&changedKeys=1"

# 300秒内至少10个key变化时保存
curl "http://localhost:1000/RDB?saveSeconds=300&changedKeys=10"
```

#### 手动触发RDB保存
```
GET /RDB/save?async={true/false}
```
**功能**: 手动触发RDB保存
**参数**:
- `async`: 是否异步保存（默认true）

**示例**:
```bash
# 异步保存
curl "http://localhost:1000/RDB/save?async=true"

# 同步保存
curl "http://localhost:1000/RDB/save?async=false"
```

### 2. AOF配置

#### 配置AOF持久化
```
GET /AOF?enable={true/false}&fsync={策略}
```
**功能**: 配置AOF持久化
**参数**:
- `enable`: 是否启用AOF
- `fsync`: 同步策略（always/everysec/no）

**示例**:
```bash
# 启用AOF，每秒同步
curl "http://localhost:1000/AOF?enable=true&fsync=everysec"

# 启用AOF，每次写入都同步
curl "http://localhost:1000/AOF?enable=true&fsync=always"

# 禁用AOF
curl "http://localhost:1000/AOF?enable=false"
```

#### 手动触发AOF重写
```
GET /AOP/restart
```
**功能**: 手动触发AOF文件重写

**示例**:
```bash
curl "http://localhost:1000/AOP/restart"
```

## 返回信息说明

### 持久化状态信息包含：
- `rdb_save_config`: RDB保存配置
- `aof_enabled`: AOF启用状态
- `aof_fsync`: AOF同步策略
- `persistence_info`: 详细的持久化信息

### 统计信息包含：
- `RDB上次保存后变更数`: 自上次RDB保存以来的key变更数量
- `RDB后台保存进行中`: 是否正在进行后台RDB保存
- `RDB最后保存时间`: 最后一次RDB保存的时间戳
- `RDB最后保存状态`: 最后一次RDB保存的状态（ok/err）
- `AOF启用状态`: AOF是否启用
- `AOF重写进行中`: 是否正在进行AOF重写
- `AOF最后重写耗时`: 最后一次AOF重写的耗时
- `AOF当前大小`: AOF文件当前大小
- `AOF基础大小`: AOF文件基础大小

## 使用建议

### 1. 日常监控
定期查看持久化状态和统计信息：
```bash
# 每日检查
curl "http://localhost:1000/redis/status"
curl "http://localhost:1000/redis/stats"
```

### 2. 配置验证
在修改配置后验证：
```bash
# 配置RDB后验证
curl "http://localhost:1000/RDB?saveSeconds=600&changedKeys=5"
curl "http://localhost:1000/redis/rdb/config"

# 配置AOF后验证
curl "http://localhost:1000/AOF?enable=true&fsync=everysec"
curl "http://localhost:1000/redis/aof/config"
```

### 3. 故障排查
当出现持久化问题时：
```bash
# 查看完整配置和状态
curl "http://localhost:1000/redis/persistence/all"

# 查看详细统计信息
curl "http://localhost:1000/redis/stats"
```

### 4. 性能优化
根据统计信息优化配置：
- 如果RDB保存频率过高，调整save策略
- 如果AOF文件过大，触发重写
- 根据业务需求选择合适的AOF同步策略

## 注意事项

1. **权限要求**: 确保Redis用户有足够权限执行配置命令
2. **性能影响**: 频繁的配置查看对性能影响很小，但手动保存操作可能影响性能
3. **配置持久性**: 通过API修改的配置在Redis重启后可能丢失，建议同时修改配置文件
4. **监控告警**: 建议设置监控告警，当持久化失败时及时通知
