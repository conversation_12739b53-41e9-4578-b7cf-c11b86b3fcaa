package com.example.lock;

import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁管理器
 * 提供锁的创建、管理和自动续期功能
 */
public class DistributedLockManager {
    
    private final JedisPool jedisPool;
    private final ConcurrentHashMap<String, RedisDistributedLock> locks = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, ReentrantRedisLock> reentrantLocks = new ConcurrentHashMap<>();
    private final ScheduledExecutorService renewalExecutor = Executors.newScheduledThreadPool(2);
    
    public DistributedLockManager(String host, int port, String password) {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(20);
        config.setMaxIdle(10);
        config.setMinIdle(5);
        config.setTestOnBorrow(true);
        
        if (password != null && !password.isEmpty()) {
            this.jedisPool = new JedisPool(config, host, port, 3000, password);
        } else {
            this.jedisPool = new JedisPool(config, host, port, 3000);
        }
        
        System.out.println("🔧 分布式锁管理器初始化完成");
    }
    
    /**
     * 创建普通分布式锁
     * @param lockKey 锁的key
     * @param expireTime 过期时间（秒）
     * @return 分布式锁实例
     */
    public RedisDistributedLock createLock(String lockKey, int expireTime) {
        return locks.computeIfAbsent(lockKey, key -> new RedisDistributedLock(jedisPool, key, expireTime));
    }
    
    /**
     * 创建普通分布式锁（默认30秒过期）
     * @param lockKey 锁的key
     * @return 分布式锁实例
     */
    public RedisDistributedLock createLock(String lockKey) {
        return createLock(lockKey, 30);
    }
    
    /**
     * 创建可重入分布式锁
     * @param lockKey 锁的key
     * @param expireTime 过期时间（秒）
     * @return 可重入分布式锁实例
     */
    public ReentrantRedisLock createReentrantLock(String lockKey, int expireTime) {
        return reentrantLocks.computeIfAbsent(lockKey, key -> new ReentrantRedisLock(jedisPool, key, expireTime));
    }
    
    /**
     * 创建可重入分布式锁（默认30秒过期）
     * @param lockKey 锁的key
     * @return 可重入分布式锁实例
     */
    public ReentrantRedisLock createReentrantLock(String lockKey) {
        return createReentrantLock(lockKey, 30);
    }
    
    /**
     * 执行带锁的操作
     * @param lockKey 锁的key
     * @param timeout 获取锁的超时时间（秒）
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithLock(String lockKey, int timeout, Supplier<T> action) {
        RedisDistributedLock lock = createLock(lockKey);
        
        try {
            if (lock.tryLock(timeout, TimeUnit.SECONDS)) {
                System.out.println("🔒 开始执行带锁操作: " + lockKey);
                return action.get();
            } else {
                throw new RuntimeException("获取锁超时: " + lockKey);
            }
        } finally {
            lock.unlock();
            System.out.println("🔓 带锁操作完成: " + lockKey);
        }
    }
    
    /**
     * 执行带可重入锁的操作
     * @param lockKey 锁的key
     * @param timeout 获取锁的超时时间（秒）
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithReentrantLock(String lockKey, int timeout, Supplier<T> action) {
        ReentrantRedisLock lock = createReentrantLock(lockKey);
        
        try {
            if (lock.tryLock(timeout, TimeUnit.SECONDS)) {
                System.out.println("🔒 开始执行带可重入锁操作: " + lockKey);
                return action.get();
            } else {
                throw new RuntimeException("获取可重入锁超时: " + lockKey);
            }
        } finally {
            lock.unlock();
            System.out.println("🔓 带可重入锁操作完成: " + lockKey);
        }
    }
    
    /**
     * 启动自动续期
     * @param lock 要续期的锁
     * @param renewInterval 续期间隔（秒）
     * @param newExpireTime 新的过期时间（秒）
     */
    public void startAutoRenewal(RedisDistributedLock lock, int renewInterval, int newExpireTime) {
        renewalExecutor.scheduleAtFixedRate(() -> {
            if (lock.isHeldByCurrentThread()) {
                boolean renewed = lock.renewLock(newExpireTime);
                if (!renewed) {
                    System.out.println("⚠️  自动续期失败，停止续期: " + lock.getLockKey());
                    return; // 停止续期
                }
            } else {
                System.out.println("🛑 锁已释放，停止自动续期: " + lock.getLockKey());
                return; // 停止续期
            }
        }, renewInterval, renewInterval, TimeUnit.SECONDS);
        
        System.out.println("🔄 启动自动续期: " + lock.getLockKey() + " (间隔: " + renewInterval + "秒)");
    }
    
    /**
     * 启动可重入锁自动续期
     * @param lock 要续期的可重入锁
     * @param renewInterval 续期间隔（秒）
     * @param newExpireTime 新的过期时间（秒）
     */
    public void startAutoRenewal(ReentrantRedisLock lock, int renewInterval, int newExpireTime) {
        renewalExecutor.scheduleAtFixedRate(() -> {
            if (lock.isHeldByCurrentThread()) {
                boolean renewed = lock.renewLock(newExpireTime);
                if (!renewed) {
                    System.out.println("⚠️  可重入锁自动续期失败，停止续期: " + lock.getLockKey());
                    return;
                }
            } else {
                System.out.println("🛑 可重入锁已释放，停止自动续期: " + lock.getLockKey());
                return;
            }
        }, renewInterval, renewInterval, TimeUnit.SECONDS);
        
        System.out.println("🔄 启动可重入锁自动续期: " + lock.getLockKey() + " (间隔: " + renewInterval + "秒)");
    }
    
    /**
     * 获取所有锁的状态
     * @return 锁状态信息
     */
    public String getAllLockStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 分布式锁状态 ===\n");
        
        status.append("普通锁 (").append(locks.size()).append("个):\n");
        locks.forEach((key, lock) -> {
            status.append("  ").append(lock.getLockInfo()).append("\n");
        });
        
        status.append("可重入锁 (").append(reentrantLocks.size()).append("个):\n");
        reentrantLocks.forEach((key, lock) -> {
            status.append("  ").append(lock.getLockInfo()).append("\n");
        });
        
        return status.toString();
    }
    
    /**
     * 清理所有锁
     */
    public void cleanupAllLocks() {
        System.out.println("🧹 开始清理所有锁...");
        
        locks.values().forEach(RedisDistributedLock::forceUnlock);
        reentrantLocks.values().forEach(ReentrantRedisLock::forceUnlock);
        
        locks.clear();
        reentrantLocks.clear();
        
        System.out.println("✅ 所有锁清理完成");
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        System.out.println("🔒 关闭分布式锁管理器...");
        
        // 停止续期服务
        renewalExecutor.shutdown();
        try {
            if (!renewalExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                renewalExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            renewalExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 清理所有锁
        cleanupAllLocks();
        
        // 关闭Redis连接池
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
        
        System.out.println("✅ 分布式锁管理器已关闭");
    }
    
    /**
     * 获取Redis连接池
     * @return Redis连接池
     */
    public JedisPool getJedisPool() {
        return jedisPool;
    }
}
