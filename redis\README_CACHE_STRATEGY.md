# Redis缓存策略防护机制

本项目实现了完整的Redis缓存策略防护机制，包括缓存穿透、缓存击穿、缓存雪崩的防护，以及布隆过滤器、互斥锁、降级策略等核心技术。

## 功能特性

### 1. 缓存穿透防护
- **问题**：大量请求查询不存在的数据，绕过缓存直接访问数据库
- **解决方案**：布隆过滤器 + 空值缓存
- **接口**：`GET /cache/penetration-protection?key=xxx`

### 2. 缓存击穿防护
- **问题**：热点数据过期时，大量并发请求同时访问数据库
- **解决方案**：互斥锁 + 双重检查
- **接口**：`GET /cache/breakdown-protection?key=xxx`

### 3. 缓存雪崩防护
- **问题**：大量缓存同时过期，导致数据库压力激增
- **解决方案**：随机过期时间
- **接口**：`GET /cache/avalanche-protection?key=xxx`

### 4. 综合防护策略
- **功能**：集成所有防护机制
- **接口**：`GET /cache/comprehensive-protection?key=xxx`

## 核心技术实现

### 布隆过滤器
```java
// 初始化布隆过滤器
BloomFilter<String> bloomFilter = BloomFilter.create(
    Funnels.stringFunnel(StandardCharsets.UTF_8),
    1000000L,  // 预期插入元素数量
    0.01       // 误判率1%
);
```

### 互斥锁机制
```java
// 使用ConcurrentHashMap管理锁
private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

// 获取锁并设置超时
ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());
if (lock.tryLock(10, TimeUnit.SECONDS)) {
    // 执行数据库查询和缓存重建
}
```

### 随机过期时间
```java
// 基础过期时间 + 随机时间
int baseExpire = 300;
int randomExpire = random.nextInt(60);
int totalExpire = baseExpire + randomExpire;
```

## API接口说明

### 基础缓存操作
- `GET /cache/penetration-protection?key=xxx` - 缓存穿透防护
- `GET /cache/breakdown-protection?key=xxx` - 缓存击穿防护  
- `GET /cache/avalanche-protection?key=xxx` - 缓存雪崩防护
- `GET /cache/comprehensive-protection?key=xxx` - 综合防护策略

### 管理接口
- `GET /cache/warmup?keys=key1,key2,key3` - 缓存预热
- `GET /cache/degrade?enable=true/false` - 开启/关闭降级模式
- `GET /cache/stats` - 获取缓存统计信息
- `GET /cache/cleanup` - 清理过期锁

## 使用示例

### 1. 测试缓存穿透防护
```bash
curl "http://localhost:8080/cache/penetration-protection?key=user:1"
# 返回：缓存命中: user:1_data_from_db

curl "http://localhost:8080/cache/penetration-protection?key=nonexistent:999999"
# 返回：数据不存在（布隆过滤器拦截）
```

### 2. 测试缓存击穿防护
```bash
curl "http://localhost:8080/cache/breakdown-protection?key=user:100"
# 返回：重建缓存: user:100_data_from_db
```

### 3. 缓存预热
```bash
curl "http://localhost:8080/cache/warmup?keys=user:1,user:2,product:1,product:2"
# 返回：预热完成，成功预热 4 个key
```

### 4. 开启降级模式
```bash
curl "http://localhost:8080/cache/degrade?enable=true"
# 返回：降级模式已开启
```

## 配置说明

### 布隆过滤器配置
- 预期插入元素：1,000,000
- 误判率：1%
- 预加载数据：user:1-1000, product:1-1000

### 缓存过期时间
- 基础过期时间：300秒（5分钟）
- 随机过期时间：0-60秒
- 空值缓存时间：60秒

### 锁超时配置
- 互斥锁超时：10秒
- 综合防护锁超时：5秒

## 性能优化建议

1. **布隆过滤器优化**
   - 根据实际数据量调整预期插入元素数量
   - 平衡误判率和内存使用

2. **锁粒度优化**
   - 使用细粒度锁，避免锁竞争
   - 定期清理无效锁，防止内存泄漏

3. **缓存策略优化**
   - 合理设置缓存过期时间
   - 使用缓存预热减少冷启动影响

## 监控指标

- 布隆过滤器拦截率
- 缓存命中率
- 数据库查询次数
- 锁等待时间
- 降级模式触发次数

## 注意事项

1. 布隆过滤器只能判断数据"可能存在"或"一定不存在"
2. 互斥锁会影响并发性能，需要合理设置超时时间
3. 降级模式应该有自动恢复机制
4. 定期监控和调整缓存策略参数
