package com.example.lock;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁实现
 * 基于SET命令的原子性操作实现分布式锁
 */
public class RedisDistributedLock {
    
    private final JedisPool jedisPool;
    private final String lockKey;
    private final String lockValue;
    private final int expireTime; // 锁过期时间（秒）
    
    // Lua脚本：原子性释放锁
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    // Lua脚本：续期锁
    private static final String RENEW_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('expire', KEYS[1], ARGV[2]) " +
        "else " +
        "    return 0 " +
        "end";
    
    public RedisDistributedLock(JedisPool jedisPool, String lockKey) {
        this(jedisPool, lockKey, 30); // 默认30秒过期
    }
    
    public RedisDistributedLock(JedisPool jedisPool, String lockKey, int expireTime) {
        this.jedisPool = jedisPool;
        this.lockKey = "lock:" + lockKey;
        this.lockValue = UUID.randomUUID().toString();
        this.expireTime = expireTime;
    }
    
    /**
     * 尝试获取锁
     * @return 是否成功获取锁
     */
    public boolean tryLock() {
        try (Jedis jedis = jedisPool.getResource()) {
            SetParams params = SetParams.setParams()
                    .nx()  // 只在key不存在时设置
                    .ex(expireTime);  // 设置过期时间
            
            String result = jedis.set(lockKey, lockValue, params);
            boolean acquired = "OK".equals(result);
            
            if (acquired) {
                System.out.println("🔒 获取锁成功: " + lockKey + " = " + lockValue);
            } else {
                System.out.println("❌ 获取锁失败: " + lockKey + " (锁已被占用)");
            }
            
            return acquired;
        } catch (Exception e) {
            System.err.println("❌ 获取锁异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 尝试获取锁（带超时）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取锁
     */
    public boolean tryLock(long timeout, TimeUnit unit) {
        long timeoutMillis = unit.toMillis(timeout);
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            if (tryLock()) {
                return true;
            }
            
            try {
                Thread.sleep(100); // 等待100ms后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        System.out.println("⏰ 获取锁超时: " + lockKey);
        return false;
    }
    
    /**
     * 释放锁
     * @return 是否成功释放锁
     */
    public boolean unlock() {
        try (Jedis jedis = jedisPool.getResource()) {
            // 使用Lua脚本确保原子性：只有锁的持有者才能释放锁
            Object result = jedis.eval(UNLOCK_SCRIPT, 
                    Collections.singletonList(lockKey), 
                    Collections.singletonList(lockValue));
            
            boolean released = Long.valueOf(1).equals(result);
            
            if (released) {
                System.out.println("🔓 释放锁成功: " + lockKey);
            } else {
                System.out.println("⚠️  释放锁失败: " + lockKey + " (锁不存在或不属于当前线程)");
            }
            
            return released;
        } catch (Exception e) {
            System.err.println("❌ 释放锁异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 续期锁
     * @param newExpireTime 新的过期时间（秒）
     * @return 是否成功续期
     */
    public boolean renewLock(int newExpireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(RENEW_SCRIPT,
                    Collections.singletonList(lockKey),
                    java.util.Arrays.asList(lockValue, String.valueOf(newExpireTime)));
            
            boolean renewed = Long.valueOf(1).equals(result);
            
            if (renewed) {
                System.out.println("🔄 锁续期成功: " + lockKey + " (新过期时间: " + newExpireTime + "秒)");
            } else {
                System.out.println("❌ 锁续期失败: " + lockKey + " (锁不存在或不属于当前线程)");
            }
            
            return renewed;
        } catch (Exception e) {
            System.err.println("❌ 锁续期异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查锁是否存在
     * @return 锁是否存在
     */
    public boolean isLocked() {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(lockKey);
        } catch (Exception e) {
            System.err.println("❌ 检查锁状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查当前线程是否持有锁
     * @return 是否持有锁
     */
    public boolean isHeldByCurrentThread() {
        try (Jedis jedis = jedisPool.getResource()) {
            String currentValue = jedis.get(lockKey);
            return lockValue.equals(currentValue);
        } catch (Exception e) {
            System.err.println("❌ 检查锁持有状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取锁的剩余过期时间
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示key不存在
     */
    public long getTimeToLive() {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.ttl(lockKey);
        } catch (Exception e) {
            System.err.println("❌ 获取锁TTL异常: " + e.getMessage());
            return -2;
        }
    }
    
    /**
     * 强制释放锁（危险操作，仅用于异常情况）
     * @return 是否成功释放
     */
    public boolean forceUnlock() {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.del(lockKey);
            boolean deleted = result > 0;
            
            if (deleted) {
                System.out.println("⚠️  强制释放锁成功: " + lockKey);
            } else {
                System.out.println("⚠️  强制释放锁失败: " + lockKey + " (锁不存在)");
            }
            
            return deleted;
        } catch (Exception e) {
            System.err.println("❌ 强制释放锁异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取锁信息
     * @return 锁信息字符串
     */
    public String getLockInfo() {
        try (Jedis jedis = jedisPool.getResource()) {
            String currentValue = jedis.get(lockKey);
            long ttl = jedis.ttl(lockKey);
            
            if (currentValue == null) {
                return "锁不存在: " + lockKey;
            } else {
                return String.format("锁信息: key=%s, value=%s, ttl=%d秒, 是否持有=%s", 
                        lockKey, currentValue, ttl, lockValue.equals(currentValue));
            }
        } catch (Exception e) {
            return "获取锁信息失败: " + e.getMessage();
        }
    }
    
    // Getter方法
    public String getLockKey() {
        return lockKey;
    }
    
    public String getLockValue() {
        return lockValue;
    }
    
    public int getExpireTime() {
        return expireTime;
    }
}
