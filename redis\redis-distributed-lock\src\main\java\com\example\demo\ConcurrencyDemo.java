package com.example.demo;

import com.example.lock.DistributedLockManager;
import com.example.lock.RedisDistributedLock;
import com.example.lock.ReentrantRedisLock;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发场景演示
 * 展示分布式锁在解决并发问题中的应用
 */
public class ConcurrencyDemo {
    
    private final DistributedLockManager lockManager;
    private final AtomicInteger counter = new AtomicInteger(0);
    private volatile int unsafeCounter = 0;
    
    public ConcurrencyDemo() {
        // 初始化锁管理器
        this.lockManager = new DistributedLockManager("localhost", 6379, "");
    }
    
    /**
     * 演示1：库存扣减场景
     */
    public void demonstrateInventoryDeduction() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("📦 演示1：分布式锁解决库存扣减并发问题");
        System.out.println("=" .repeat(60));
        
        // 初始库存
        final int initialStock = 100;
        final String stockKey = "product:stock:1001";
        
        // 设置初始库存
        try (var jedis = lockManager.getJedisPool().getResource()) {
            jedis.set(stockKey, String.valueOf(initialStock));
            System.out.println("📦 初始库存设置: " + initialStock);
        }
        
        // 模拟10个线程同时扣减库存
        int threadCount = 10;
        int deductionPerThread = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        System.out.println("🚀 启动 " + threadCount + " 个线程，每个线程扣减 " + deductionPerThread + " 个库存");
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i + 1;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < deductionPerThread; j++) {
                        deductStock(stockKey, 1, threadId);
                        Thread.sleep(50); // 模拟业务处理时间
                    }
                } catch (Exception e) {
                    System.err.println("线程" + threadId + "执行异常: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查最终库存
        try (var jedis = lockManager.getJedisPool().getResource()) {
            String finalStock = jedis.get(stockKey);
            System.out.println("📊 最终库存: " + finalStock);
            System.out.println("📊 预期库存: " + (initialStock - threadCount * deductionPerThread));
        }
        
        executor.shutdown();
    }
    
    /**
     * 扣减库存（带分布式锁）
     */
    private void deductStock(String stockKey, int quantity, int threadId) {
        RedisDistributedLock lock = lockManager.createLock("lock:" + stockKey, 10);
        
        try {
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                try (var jedis = lockManager.getJedisPool().getResource()) {
                    // 获取当前库存
                    String stockStr = jedis.get(stockKey);
                    int currentStock = Integer.parseInt(stockStr);
                    
                    if (currentStock >= quantity) {
                        // 模拟业务处理时间
                        Thread.sleep(10);
                        
                        // 扣减库存
                        int newStock = currentStock - quantity;
                        jedis.set(stockKey, String.valueOf(newStock));
                        
                        System.out.println("✅ 线程" + threadId + " 扣减成功: " + currentStock + " -> " + newStock);
                    } else {
                        System.out.println("❌ 线程" + threadId + " 库存不足: " + currentStock);
                    }
                }
            } else {
                System.out.println("⏰ 线程" + threadId + " 获取锁超时");
            }
        } catch (Exception e) {
            System.err.println("❌ 线程" + threadId + " 扣减库存异常: " + e.getMessage());
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 演示2：可重入锁场景
     */
    public void demonstrateReentrantLock() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("🔄 演示2：可重入分布式锁");
        System.out.println("=" .repeat(60));
        
        ReentrantRedisLock lock = lockManager.createReentrantLock("reentrant_demo", 30);
        
        try {
            recursiveMethod(lock, 3);
        } finally {
            // 确保锁被完全释放
            while (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 递归方法演示可重入锁
     */
    private void recursiveMethod(ReentrantRedisLock lock, int depth) {
        if (depth <= 0) return;
        
        System.out.println("🔒 尝试获取锁 (深度: " + depth + ")");
        
        if (lock.tryLock()) {
            try {
                System.out.println("✅ 获取锁成功 (深度: " + depth + ", 重入次数: " + lock.getReentrantCount() + ")");
                
                // 模拟业务处理
                Thread.sleep(100);
                
                // 递归调用
                recursiveMethod(lock, depth - 1);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                lock.unlock();
                System.out.println("🔓 释放锁 (深度: " + depth + ", 剩余重入次数: " + lock.getReentrantCount() + ")");
            }
        } else {
            System.out.println("❌ 获取锁失败 (深度: " + depth + ")");
        }
    }
    
    /**
     * 演示3：对比有锁和无锁的并发安全性
     */
    public void demonstrateConcurrencySafety() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("⚖️  演示3：对比有锁和无锁的并发安全性");
        System.out.println("=" .repeat(60));
        
        int threadCount = 20;
        int incrementsPerThread = 100;
        
        // 无锁操作
        System.out.println("🚫 无锁并发操作:");
        testUnsafeConcurrency(threadCount, incrementsPerThread);
        
        // 有锁操作
        System.out.println("\n🔒 有锁并发操作:");
        testSafeConcurrency(threadCount, incrementsPerThread);
    }
    
    /**
     * 测试无锁并发（不安全）
     */
    private void testUnsafeConcurrency(int threadCount, int incrementsPerThread) {
        unsafeCounter = 0;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < incrementsPerThread; j++) {
                        // 不安全的操作
                        int temp = unsafeCounter;
                        Thread.sleep(1); // 模拟处理时间，增加竞态条件概率
                        unsafeCounter = temp + 1;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long endTime = System.currentTimeMillis();
        int expected = threadCount * incrementsPerThread;
        
        System.out.println("  预期结果: " + expected);
        System.out.println("  实际结果: " + unsafeCounter);
        System.out.println("  数据丢失: " + (expected - unsafeCounter));
        System.out.println("  耗时: " + (endTime - startTime) + "ms");
        
        executor.shutdown();
    }
    
    /**
     * 测试有锁并发（安全）
     */
    private void testSafeConcurrency(int threadCount, int incrementsPerThread) {
        counter.set(0);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < incrementsPerThread; j++) {
                        // 使用分布式锁保护的操作
                        lockManager.executeWithLock("counter_lock", 5, () -> {
                            try {
                                int current = counter.get();
                                Thread.sleep(1); // 模拟处理时间
                                counter.set(current + 1);
                                return null;
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                return null;
                            }
                        });
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long endTime = System.currentTimeMillis();
        int expected = threadCount * incrementsPerThread;
        
        System.out.println("  预期结果: " + expected);
        System.out.println("  实际结果: " + counter.get());
        System.out.println("  数据丢失: " + (expected - counter.get()));
        System.out.println("  耗时: " + (endTime - startTime) + "ms");
        
        executor.shutdown();
    }
    
    /**
     * 演示4：锁的自动续期
     */
    public void demonstrateAutoRenewal() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("🔄 演示4：锁的自动续期");
        System.out.println("=" .repeat(60));
        
        RedisDistributedLock lock = lockManager.createLock("auto_renewal_demo", 5); // 5秒过期
        
        if (lock.tryLock()) {
            try {
                System.out.println("🔒 获取锁成功，开始长时间任务...");
                System.out.println("📊 初始TTL: " + lock.getTimeToLive() + "秒");
                
                // 启动自动续期（每3秒续期一次，续期到10秒）
                lockManager.startAutoRenewal(lock, 3, 10);
                
                // 模拟长时间任务（15秒）
                for (int i = 1; i <= 15; i++) {
                    Thread.sleep(1000);
                    System.out.println("⏰ 任务进行中... " + i + "秒 (TTL: " + lock.getTimeToLive() + "秒)");
                }
                
                System.out.println("✅ 长时间任务完成");
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                lock.unlock();
                System.out.println("🔓 锁已释放");
            }
        } else {
            System.out.println("❌ 获取锁失败");
        }
    }
    
    /**
     * 运行所有演示
     */
    public void runAllDemos() {
        System.out.println("🚀 开始Redis分布式锁演示");
        
        try {
            demonstrateInventoryDeduction();
            demonstrateReentrantLock();
            demonstrateConcurrencySafety();
            demonstrateAutoRenewal();
            
            // 显示所有锁的状态
            System.out.println("\n" + lockManager.getAllLockStatus());
            
        } catch (Exception e) {
            System.err.println("❌ 演示过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            lockManager.shutdown();
        }
        
        System.out.println("\n✅ 所有演示完成！");
    }
    
    public static void main(String[] args) {
        ConcurrencyDemo demo = new ConcurrencyDemo();
        demo.runAllDemos();
    }
}
