package com.zhentao.controller;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.zhentao.service.CacheBreakdownProtectionService;
import com.zhentao.service.RedisPersistenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@RestController
public class Controller {
    @Autowired
    RedisTemplate<String,String> redisTemplate;

    @Autowired
    CacheBreakdownProtectionService cacheBreakdownService;

    @Autowired
    RedisPersistenceService persistenceService;

    private BloomFilter<String> bloomFilter;




    @RequestMapping("/one")
    public String one(){
        Jedis jedis=new Jedis("192.168.252.131",6379);
        jedis.auth("root");
        jedis.set("user:1","用户1");
        return "one";
    }

    @RequestMapping("/two")
    public String two(String key){
            // 预计插入元素数量
            long error = 1000000L;
            // 误判率
            double err = 0.01;
            bloomFilter = BloomFilter.create(
                    Funnels.stringFunnel(StandardCharsets.UTF_8),
                    error,
                    err
            );
        bloomFilter.put(key);
        boolean b = bloomFilter.mightContain(key);
        if (b){
            return "数据存在";
        }else{
            return "数据不存在";
        }
    }





    // ==================== Redis持久化配置接口 ====================

    /**
     * 配置RDB持久化
     * @param saveSeconds RDB保存间隔时间（秒）
     * @param changedKeys 变更的key数量阈值
     * @return 配置结果
     */
    @RequestMapping("/RDB")
    public String configureRDB(@RequestParam(defaultValue = "900") int saveSeconds,
                              @RequestParam(defaultValue = "1") int changedKeys) {
        try {
            return persistenceService.configureRDB(saveSeconds, changedKeys);
        } catch (Exception e) {
            return "RDB配置失败: " + e.getMessage();
        }
    }

    /**
     * 配置AOF持久化
     * @param enable 是否启用AOF
     * @param fsync AOF同步策略 (always/everysec/no)
     * @return 配置结果
     */
    @RequestMapping("/AOF")
    public String configureAOF(@RequestParam(defaultValue = "true") boolean enable,
                              @RequestParam(defaultValue = "everysec") String fsync) {
        try {
            return persistenceService.configureAOF(enable, fsync);
        } catch (Exception e) {
            return "AOF配置失败: " + e.getMessage();
        }
    }

}
