package com.zhentao.controller;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import java.nio.charset.StandardCharsets;

@RestController
public class Controller {
    @Autowired
    RedisTemplate<String,String> redisTemplate;
    BloomFilter<String> bloomFilter;
    @RequestMapping("/one")
    public String one(){
        Jedis jedis=new Jedis("192.168.252.131",6379);
        jedis.auth("root");
        jedis.set("user:1","用户1");
        return "one";
    }
    @RequestMapping("/two")
    public String two(String key){
            // 预计插入元素数量
            long error = 1000000L;
            // 误判率
            double err = 0.01;
            bloomFilter = BloomFilter.create(
                    Funnels.stringFunnel(StandardCharsets.UTF_8),
                    error,
                    err
            );
        bloomFilter.put(key);
        boolean b = bloomFilter.mightContain(key);
        if (b){
            return "数据存在";
        }else{
            return "数据不存在";
        }
    }
}
