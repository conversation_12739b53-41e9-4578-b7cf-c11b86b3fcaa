package com.zhentao.controller;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@RestController
public class Controller {
    @Autowired
    RedisTemplate<String,String> redisTemplate;

    // 布隆过滤器 - 防止缓存穿透
    private BloomFilter<String> bloomFilter;

    // 互斥锁映射 - 防止缓存击穿
    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    // 随机数生成器 - 防止缓存雪崩
    private final Random random = new Random();

    // 降级标识
    private volatile boolean degradeMode = false;

    @PostConstruct
    public void init() {
        // 初始化布隆过滤器
        long expectedInsertions = 1000000L;
        double fpp = 0.01;
        bloomFilter = BloomFilter.create(
                Funnels.stringFunnel(StandardCharsets.UTF_8),
                expectedInsertions,
                fpp
        );

        // 预加载一些热点数据到布隆过滤器
        preloadBloomFilter();
    }

    /**
     * 预加载布隆过滤器
     */
    private void preloadBloomFilter() {
        // 这里可以从数据库加载已存在的key
        for (int i = 1; i <= 1000; i++) {
            bloomFilter.put("user:" + i);
            bloomFilter.put("product:" + i);
        }
    }

    @RequestMapping("/one")
    public String one(){
        Jedis jedis=new Jedis("192.168.252.131",6379);
        jedis.auth("root");
        jedis.set("user:1","用户1");
        return "one";
    }

    @RequestMapping("/two")
    public String two(String key){
            // 预计插入元素数量
            long error = 1000000L;
            // 误判率
            double err = 0.01;
            bloomFilter = BloomFilter.create(
                    Funnels.stringFunnel(StandardCharsets.UTF_8),
                    error,
                    err
            );
        bloomFilter.put(key);
        boolean b = bloomFilter.mightContain(key);
        if (b){
            return "数据存在";
        }else{
            return "数据不存在";
        }
    }

    /**
     * 防缓存穿透 - 使用布隆过滤器
     * @param key 缓存key
     * @return 数据或null
     */
    @GetMapping("/cache/penetration-protection")
    public String getCacheWithPenetrationProtection(@RequestParam String key) {
        try {
            // 1. 检查布隆过滤器
            if (!bloomFilter.mightContain(key)) {
                return "数据不存在（布隆过滤器拦截）";
            }

            // 2. 查询Redis缓存
            String cacheValue = redisTemplate.opsForValue().get(key);
            if (cacheValue != null) {
                return "缓存命中: " + cacheValue;
            }

            // 3. 查询数据库（模拟）
            String dbValue = queryDatabase(key);
            if (dbValue != null) {
                // 4. 写入缓存，设置过期时间
                redisTemplate.opsForValue().set(key, dbValue, 300, TimeUnit.SECONDS);
                return "数据库查询: " + dbValue;
            } else {
                // 5. 数据不存在，缓存空值防止穿透
                redisTemplate.opsForValue().set(key, "", 60, TimeUnit.SECONDS);
                return "数据不存在";
            }
        } catch (Exception e) {
            return "查询异常: " + e.getMessage();
        }
    }

    /**
     * 防缓存击穿 - 使用互斥锁
     * @param key 缓存key
     * @return 数据
     */
    @GetMapping("/cache/breakdown-protection")
    public String getCacheWithBreakdownProtection(@RequestParam String key) {
        try {
            // 1. 查询Redis缓存
            String cacheValue = redisTemplate.opsForValue().get(key);
            if (cacheValue != null && !cacheValue.isEmpty()) {
                return "缓存命中: " + cacheValue;
            }

            // 2. 获取互斥锁
            ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());

            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    // 3. 双重检查缓存
                    cacheValue = redisTemplate.opsForValue().get(key);
                    if (cacheValue != null && !cacheValue.isEmpty()) {
                        return "缓存命中（双重检查）: " + cacheValue;
                    }

                    // 4. 查询数据库
                    String dbValue = queryDatabase(key);
                    if (dbValue != null) {
                        // 5. 重建缓存
                        redisTemplate.opsForValue().set(key, dbValue, 300, TimeUnit.SECONDS);
                        return "重建缓存: " + dbValue;
                    } else {
                        return "数据不存在";
                    }
                } finally {
                    lock.unlock();
                    // 清理锁映射，避免内存泄漏
                    lockMap.remove(key);
                }
            } else {
                // 6. 获取锁超时，返回降级数据
                return "系统繁忙，请稍后重试";
            }
        } catch (Exception e) {
            return "查询异常: " + e.getMessage();
        }
    }

    /**
     * 防缓存雪崩 - 使用随机过期时间
     * @param key 缓存key
     * @return 数据
     */
    @GetMapping("/cache/avalanche-protection")
    public String getCacheWithAvalancheProtection(@RequestParam String key) {
        try {
            // 1. 查询Redis缓存
            String cacheValue = redisTemplate.opsForValue().get(key);
            if (cacheValue != null && !cacheValue.isEmpty()) {
                return "缓存命中: " + cacheValue;
            }

            // 2. 查询数据库
            String dbValue = queryDatabase(key);
            if (dbValue != null) {
                // 3. 设置随机过期时间，防止同时失效
                int baseExpire = 300; // 基础过期时间5分钟
                int randomExpire = random.nextInt(60); // 随机0-60秒
                int totalExpire = baseExpire + randomExpire;

                redisTemplate.opsForValue().set(key, dbValue, totalExpire, TimeUnit.SECONDS);
                return "数据库查询（随机过期" + totalExpire + "s）: " + dbValue;
            } else {
                return "数据不存在";
            }
        } catch (Exception e) {
            return "查询异常: " + e.getMessage();
        }
    }

    /**
     * 综合防护策略 - 集成所有防护机制
     * @param key 缓存key
     * @return 数据
     */
    @GetMapping("/cache/comprehensive-protection")
    public String getCacheWithComprehensiveProtection(@RequestParam String key) {
        try {
            // 降级模式检查
            if (degradeMode) {
                return getDegradedData(key);
            }

            // 1. 布隆过滤器检查（防穿透）
            if (!bloomFilter.mightContain(key)) {
                return "数据不存在（布隆过滤器拦截）";
            }

            // 2. 查询Redis缓存
            String cacheValue = redisTemplate.opsForValue().get(key);
            if (cacheValue != null && !cacheValue.isEmpty()) {
                return "缓存命中: " + cacheValue;
            }

            // 3. 互斥锁防击穿
            ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());

            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                try {
                    // 双重检查
                    cacheValue = redisTemplate.opsForValue().get(key);
                    if (cacheValue != null && !cacheValue.isEmpty()) {
                        return "缓存命中（双重检查）: " + cacheValue;
                    }

                    // 4. 查询数据库
                    String dbValue = queryDatabase(key);
                    if (dbValue != null) {
                        // 5. 随机过期时间防雪崩
                        int baseExpire = 300;
                        int randomExpire = random.nextInt(60);
                        int totalExpire = baseExpire + randomExpire;

                        redisTemplate.opsForValue().set(key, dbValue, totalExpire, TimeUnit.SECONDS);
                        return "综合防护查询: " + dbValue;
                    } else {
                        // 缓存空值
                        redisTemplate.opsForValue().set(key, "", 60, TimeUnit.SECONDS);
                        return "数据不存在";
                    }
                } finally {
                    lock.unlock();
                    lockMap.remove(key);
                }
            } else {
                // 获取锁超时，返回降级数据
                return getDegradedData(key);
            }
        } catch (Exception e) {
            // 异常时启用降级模式
            degradeMode = true;
            return getDegradedData(key);
        }
    }

    /**
     * 模拟数据库查询
     * @param key 查询key
     * @return 数据或null
     */
    private String queryDatabase(String key) {
        try {
            // 模拟数据库查询延迟
            Thread.sleep(100);

            // 模拟数据库中存在的数据
            if (key.startsWith("user:") || key.startsWith("product:")) {
                String id = key.substring(key.indexOf(":") + 1);
                try {
                    int numId = Integer.parseInt(id);
                    if (numId <= 1000) {
                        return key + "_data_from_db";
                    }
                } catch (NumberFormatException e) {
                    // 非数字ID，返回null
                }
            }
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 降级数据获取
     * @param key 缓存key
     * @return 降级数据
     */
    private String getDegradedData(String key) {
        return "系统繁忙，返回降级数据: " + key + "_default";
    }

    /**
     * 预热缓存
     * @param keys 需要预热的key列表
     * @return 预热结果
     */
    @GetMapping("/cache/warmup")
    public String warmupCache(@RequestParam String keys) {
        try {
            String[] keyArray = keys.split(",");
            int successCount = 0;

            for (String key : keyArray) {
                String dbValue = queryDatabase(key.trim());
                if (dbValue != null) {
                    // 添加到布隆过滤器
                    bloomFilter.put(key.trim());
                    // 设置缓存
                    int randomExpire = 300 + random.nextInt(60);
                    redisTemplate.opsForValue().set(key.trim(), dbValue, randomExpire, TimeUnit.SECONDS);
                    successCount++;
                }
            }

            return "预热完成，成功预热 " + successCount + " 个key";
        } catch (Exception e) {
            return "预热失败: " + e.getMessage();
        }
    }

    /**
     * 开启/关闭降级模式
     * @param enable 是否开启
     * @return 操作结果
     */
    @GetMapping("/cache/degrade")
    public String toggleDegradeMode(@RequestParam boolean enable) {
        degradeMode = enable;
        return "降级模式已" + (enable ? "开启" : "关闭");
    }

    /**
     * 获取缓存统计信息
     * @return 统计信息
     */
    @GetMapping("/cache/stats")
    public String getCacheStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("缓存统计信息:\n");
            stats.append("降级模式: ").append(degradeMode ? "开启" : "关闭").append("\n");
            stats.append("布隆过滤器预期误判率: 1%\n");
            stats.append("当前锁数量: ").append(lockMap.size()).append("\n");

            return stats.toString();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 清理过期锁
     * @return 清理结果
     */
    @GetMapping("/cache/cleanup")
    public String cleanupLocks() {
        try {
            int beforeSize = lockMap.size();
            lockMap.entrySet().removeIf(entry -> !entry.getValue().isLocked());
            int afterSize = lockMap.size();
            return "清理完成，清理了 " + (beforeSize - afterSize) + " 个无效锁";
        } catch (Exception e) {
            return "清理失败: " + e.getMessage();
        }
    }
}
