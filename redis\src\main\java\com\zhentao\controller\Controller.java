package com.zhentao.controller;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.zhentao.service.CacheBreakdownProtectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@RestController
public class Controller {
    @Autowired
    RedisTemplate<String,String> redisTemplate;

    @Autowired
    CacheBreakdownProtectionService cacheBreakdownService;

    // 布隆过滤器 - 防止缓存穿透
    private BloomFilter<String> bloomFilter;

    // 互斥锁映射 - 防止缓存击穿
    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    // 随机数生成器 - 防止缓存雪崩
    private final Random random = new Random();

    // 降级标识
    private volatile boolean degradeMode = false;

    @PostConstruct
    public void init() {
        // 初始化布隆过滤器
        long expectedInsertions = 1000000L;
        double fpp = 0.01;
        bloomFilter = BloomFilter.create(
                Funnels.stringFunnel(StandardCharsets.UTF_8),
                expectedInsertions,
                fpp
        );

        // 预加载一些热点数据到布隆过滤器
        preloadBloomFilter();
    }

    /**
     * 预加载布隆过滤器
     */
    private void preloadBloomFilter() {
        // 这里可以从数据库加载已存在的key
        for (int i = 1; i <= 1000; i++) {
            bloomFilter.put("user:" + i);
            bloomFilter.put("product:" + i);
        }
    }

    @RequestMapping("/one")
    public String one(){
        Jedis jedis=new Jedis("192.168.252.131",6379);
        jedis.auth("root");
        jedis.set("user:1","用户1");
        return "one";
    }

    @RequestMapping("/two")
    public String two(String key){
            // 预计插入元素数量
            long error = 1000000L;
            // 误判率
            double err = 0.01;
            bloomFilter = BloomFilter.create(
                    Funnels.stringFunnel(StandardCharsets.UTF_8),
                    error,
                    err
            );
        bloomFilter.put(key);
        boolean b = bloomFilter.mightContain(key);
        if (b){
            return "数据存在";
        }else{
            return "数据不存在";
        }
    }

    /**
     * 缓存击穿防护 - 布隆过滤器 + 互斥锁
     * 解决热点数据过期时大量并发请求同时访问数据库的问题
     * @param key 缓存key
     * @return 数据结果
     */
    @GetMapping("/cache/breakdown-protection")
    public String cacheBreakdownProtection(@RequestParam String key) {
        try {
            // 第一步：布隆过滤器检查数据是否可能存在
            if (!bloomFilter.mightContain(key)) {
                return "数据不存在（布隆过滤器拦截）- 避免无效的数据库查询";
            }

            // 第二步：查询Redis缓存
            String cacheValue = redisTemplate.opsForValue().get(key);
            if (cacheValue != null && !cacheValue.isEmpty()) {
                return "缓存命中: " + cacheValue;
            }

            // 第三步：缓存未命中，使用互斥锁防止缓存击穿
            return rebuildCacheWithLock(key);

        } catch (Exception e) {
            return "查询异常: " + e.getMessage();
        }
    }

    /**
     * 使用互斥锁重建缓存
     * @param key 缓存key
     * @return 查询结果
     */
    private String rebuildCacheWithLock(String key) {
        // 获取或创建该key对应的锁
        ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());

        try {
            // 尝试获取锁，设置超时时间防止死锁
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    // 双重检查：再次查询缓存，可能其他线程已经重建了缓存
                    String cacheValue = redisTemplate.opsForValue().get(key);
                    if (cacheValue != null && !cacheValue.isEmpty()) {
                        return "缓存命中（双重检查）: " + cacheValue;
                    }

                    // 查询数据库
                    String dbValue = queryDatabase(key);
                    if (dbValue != null) {
                        // 重建缓存，设置过期时间
                        redisTemplate.opsForValue().set(key, dbValue, 300, TimeUnit.SECONDS);
                        return "重建缓存成功: " + dbValue;
                    } else {
                        // 数据不存在，设置短期空值缓存防止频繁查询
                        redisTemplate.opsForValue().set(key, "NULL", 60, TimeUnit.SECONDS);
                        return "数据不存在";
                    }
                } finally {
                    // 释放锁
                    lock.unlock();
                    // 清理锁映射，避免内存泄漏
                    lockMap.remove(key);
                }
            } else {
                // 获取锁超时，返回降级数据
                return "系统繁忙，请稍后重试（锁超时）";
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return "线程被中断: " + e.getMessage();
        }
    }

    /**
     * 模拟数据库查询
     * @param key 查询key
     * @return 数据或null
     */
    private String queryDatabase(String key) {
        try {
            // 模拟数据库查询延迟
            Thread.sleep(100);

            // 模拟数据库中存在的数据
            if (key.startsWith("user:") || key.startsWith("product:")) {
                String id = key.substring(key.indexOf(":") + 1);
                try {
                    int numId = Integer.parseInt(id);
                    if (numId <= 1000) {
                        return key + "_data_from_database";
                    }
                } catch (NumberFormatException e) {
                    // 非数字ID，返回null
                }
            }
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 高并发场景下的缓存击穿防护测试
     * @param key 缓存key
     * @return 测试结果
     */
    @GetMapping("/cache/breakdown-test")
    public String testCacheBreakdown(@RequestParam String key) {
        // 先删除缓存，模拟缓存过期
        redisTemplate.delete(key);

        // 确保key在布隆过滤器中
        bloomFilter.put(key);

        // 模拟高并发访问
        long startTime = System.currentTimeMillis();
        String result = cacheBreakdownProtection(key);
        long endTime = System.currentTimeMillis();

        return String.format("测试结果: %s, 耗时: %dms", result, (endTime - startTime));
    }

    /**
     * 批量预热缓存并添加到布隆过滤器
     * @param keys 需要预热的key列表，用逗号分隔
     * @return 预热结果
     */
    @GetMapping("/cache/warmup")
    public String warmupCache(@RequestParam String keys) {
        try {
            String[] keyArray = keys.split(",");
            int successCount = 0;

            for (String key : keyArray) {
                key = key.trim();

                // 查询数据库
                String dbValue = queryDatabase(key);
                if (dbValue != null) {
                    // 添加到布隆过滤器
                    bloomFilter.put(key);

                    // 设置缓存，添加随机过期时间防止雪崩
                    int randomExpire = 300 + random.nextInt(60);
                    redisTemplate.opsForValue().set(key, dbValue, randomExpire, TimeUnit.SECONDS);

                    successCount++;
                }
            }

            return String.format("预热完成，成功预热 %d/%d 个key", successCount, keyArray.length);
        } catch (Exception e) {
            return "预热失败: " + e.getMessage();
        }
    }

    /**
     * 获取缓存击穿防护统计信息
     * @return 统计信息
     */
    @GetMapping("/cache/breakdown-stats")
    public String getBreakdownStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("=== 缓存击穿防护统计 ===\n");
            stats.append("当前活跃锁数量: ").append(lockMap.size()).append("\n");
            stats.append("布隆过滤器配置: 预期100万元素，误判率1%\n");
            stats.append("降级模式状态: ").append(degradeMode ? "开启" : "关闭").append("\n");

            // 显示当前锁的详细信息
            if (!lockMap.isEmpty()) {
                stats.append("当前锁详情:\n");
                lockMap.forEach((k, v) -> {
                    stats.append("  - ").append(k).append(": ")
                         .append(v.isLocked() ? "已锁定" : "未锁定").append("\n");
                });
            }

            return stats.toString();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 清理无效锁
     * @return 清理结果
     */
    @GetMapping("/cache/cleanup-locks")
    public String cleanupLocks() {
        try {
            int beforeSize = lockMap.size();

            // 移除未被锁定的锁对象
            lockMap.entrySet().removeIf(entry -> !entry.getValue().isLocked());

            int afterSize = lockMap.size();
            int cleanedCount = beforeSize - afterSize;

            return String.format("锁清理完成，清理了 %d 个无效锁，剩余 %d 个活跃锁",
                               cleanedCount, afterSize);
        } catch (Exception e) {
            return "清理锁失败: " + e.getMessage();
        }
    }

    /**
     * 使用专业服务的缓存击穿防护
     * @param key 缓存key
     * @return 查询结果
     */
    @GetMapping("/cache/service-breakdown-protection")
    public String serviceBreakdownProtection(@RequestParam String key) {
        try {
            // 使用专业的缓存击穿防护服务
            String result = cacheBreakdownService.getWithBreakdownProtection(
                key,
                this::queryDatabase, // 数据加载函数
                300 // 缓存过期时间（秒）
            );

            if (result != null) {
                return "服务查询成功: " + result;
            } else {
                return "数据不存在";
            }
        } catch (Exception e) {
            return "服务查询异常: " + e.getMessage();
        }
    }

    /**
     * 批量预加载布隆过滤器
     * @param keys 需要预加载的key列表，用逗号分隔
     * @return 预加载结果
     */
    @GetMapping("/cache/batch-preload")
    public String batchPreloadBloomFilter(@RequestParam String keys) {
        try {
            String[] keyArray = keys.split(",");

            // 批量添加到布隆过滤器
            cacheBreakdownService.batchAddToBloomFilter(keyArray);

            return String.format("批量预加载完成，共处理 %d 个key", keyArray.length);
        } catch (Exception e) {
            return "批量预加载失败: " + e.getMessage();
        }
    }

    /**
     * 检查key是否在布隆过滤器中
     * @param key 要检查的key
     * @return 检查结果
     */
    @GetMapping("/cache/bloom-check")
    public String checkBloomFilter(@RequestParam String key) {
        try {
            boolean mightContain = cacheBreakdownService.mightContain(key);
            return String.format("布隆过滤器检查结果: key '%s' %s",
                               key, mightContain ? "可能存在" : "一定不存在");
        } catch (Exception e) {
            return "布隆过滤器检查失败: " + e.getMessage();
        }
    }

    /**
     * 获取详细统计信息
     * @return 统计信息
     */
    @GetMapping("/cache/service-stats")
    public String getServiceStatistics() {
        try {
            return cacheBreakdownService.getStatistics();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 重置统计信息
     * @return 重置结果
     */
    @GetMapping("/cache/reset-stats")
    public String resetStatistics() {
        try {
            cacheBreakdownService.resetStatistics();
            return "统计信息已重置";
        } catch (Exception e) {
            return "重置统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 强制删除缓存
     * @param key 要删除的缓存key
     * @return 删除结果
     */
    @GetMapping("/cache/evict")
    public String evictCache(@RequestParam String key) {
        try {
            cacheBreakdownService.evictCache(key);
            return "缓存已删除: " + key;
        } catch (Exception e) {
            return "删除缓存失败: " + e.getMessage();
        }
    }

    /**
     * 压力测试 - 模拟高并发缓存击穿场景
     * @param key 测试key
     * @param threads 并发线程数
     * @return 测试结果
     */
    @GetMapping("/cache/stress-test")
    public String stressTest(@RequestParam String key, @RequestParam(defaultValue = "10") int threads) {
        try {
            // 先删除缓存，模拟缓存过期
            cacheBreakdownService.evictCache(key);

            // 确保key在布隆过滤器中
            cacheBreakdownService.addToBloomFilter(key);

            long startTime = System.currentTimeMillis();

            // 模拟多线程并发访问
            Thread[] threadArray = new Thread[threads];
            for (int i = 0; i < threads; i++) {
                threadArray[i] = new Thread(() -> {
                    cacheBreakdownService.getWithBreakdownProtection(key, this::queryDatabase, 300);
                });
                threadArray[i].start();
            }

            // 等待所有线程完成
            for (Thread thread : threadArray) {
                thread.join();
            }

            long endTime = System.currentTimeMillis();

            return String.format("压力测试完成: %d个线程并发访问，耗时: %dms",
                               threads, (endTime - startTime));
        } catch (Exception e) {
            return "压力测试失败: " + e.getMessage();
        }
    }

}
