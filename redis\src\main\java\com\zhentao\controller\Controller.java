package com.zhentao.controller;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.zhentao.service.RedisPersistenceService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import java.nio.charset.StandardCharsets;

@RestController
public class Controller {
    @Autowired
    RedisTemplate<String,String> redisTemplate;


    @Autowired
    RedisPersistenceService persistenceService;

    private BloomFilter<String> bloomFilter;




    @RequestMapping("/one")
    public String one(){
        Jedis jedis=new Jedis("192.168.252.131",6379);
        jedis.auth("root");
        jedis.set("user:1","用户1");
        return "one";
    }

    @RequestMapping("/two")
    public String two(String key){
            // 预计插入元素数量
            long error = 1000000L;
            // 误判率
            double err = 0.01;
            bloomFilter = BloomFilter.create(
                    Funnels.stringFunnel(StandardCharsets.UTF_8),
                    error,
                    err
            );
        bloomFilter.put(key);
        boolean b = bloomFilter.mightContain(key);
        if (b){
            return "数据存在";
        }else{
            return "数据不存在";
        }
    }





    // ==================== Redis持久化配置接口 ====================

    @RequestMapping("/RDB")
    public String configureRDB() {
        return persistenceService.configureRDB(900, 1);
    }

    @RequestMapping("/AOF")
    public String configureAOF() {
        return persistenceService.configureAOF(true, "everysec");
    }

    @RequestMapping("/RDB/save")
    public String triggerRDBSave() {
        return persistenceService.triggerRDBSave(true);
    }

    /**
     * 手动触发AOF重写
     * @return 重写结果
     */
    @RequestMapping("/AOP/restart")
    public String triggerAOFRewrite() {
        return persistenceService.triggerAOFRewrite();
    }




    @RequestMapping("/rdb/config")
    public String getRDBConfig() {
            var config = persistenceService.getRedisConfig("*save*");
            config.putAll(persistenceService.getRedisConfig("*rdb*"));

            StringBuilder result = new StringBuilder();
            result.append("=== RDB持久化配置 ===\n");

            config.forEach((key, value) -> {
                result.append(key).append(": ").append(value).append("\n");
            });
            return result.toString();
    }


    @RequestMapping("/aof/config")
    public String getAOFConfig() {
            var config = persistenceService.getRedisConfig("*aof*");
            config.putAll(persistenceService.getRedisConfig("*append*"));

            StringBuilder result = new StringBuilder();
            result.append("=== AOF持久化配置 ===\n");

            config.forEach((key, value) -> {
                result.append(key).append(": ").append(value).append("\n");
            });

            return result.toString();
    }

}
