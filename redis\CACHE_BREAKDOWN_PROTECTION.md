# 缓存击穿防护机制实现

## 什么是缓存击穿

缓存击穿是指**热点数据**在缓存过期的瞬间，大量并发请求同时访问这个数据，由于缓存中没有数据，所有请求都会直接访问数据库，造成数据库瞬间压力激增的现象。

### 缓存击穿的特点
- 针对**单个热点key**
- 发生在**缓存过期瞬间**
- **高并发**访问同一个key
- 可能导致数据库**瞬间压力激增**

## 解决方案：布隆过滤器 + 互斥锁

### 1. 布隆过滤器（第一道防线）
```java
// 初始化布隆过滤器
BloomFilter<String> bloomFilter = BloomFilter.create(
    Funnels.stringFunnel(StandardCharsets.UTF_8),
    1000000L,  // 预期插入100万个元素
    0.01       // 1%的误判率
);
```

**作用**：
- 快速判断数据是否**可能存在**
- 如果返回false，数据**一定不存在**，直接返回
- 如果返回true，数据**可能存在**，继续查询

**优势**：
- 时间复杂度O(1)
- 空间效率高
- 避免无效的数据库查询

### 2. 互斥锁（第二道防线）
```java
// 使用ConcurrentHashMap管理每个key的锁
private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

// 获取锁
ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());
if (lock.tryLock(10, TimeUnit.SECONDS)) {
    try {
        // 重建缓存逻辑
    } finally {
        lock.unlock();
        lockMap.remove(key); // 避免内存泄漏
    }
}
```

**作用**：
- 确保同一时间只有**一个线程**重建缓存
- 其他线程等待或返回降级数据
- 避免数据库被大量并发请求击穿

### 3. 双重检查（优化策略）
```java
// 获取锁后再次检查缓存
String cacheValue = redisTemplate.opsForValue().get(key);
if (cacheValue != null) {
    return cacheValue; // 其他线程已经重建了缓存
}
// 执行数据库查询和缓存重建
```

**作用**：
- 避免重复的数据库查询
- 提高并发性能

## 完整的防护流程

```
请求 → 布隆过滤器检查 → Redis缓存查询 → 互斥锁 → 双重检查 → 数据库查询 → 缓存重建
  ↓           ↓              ↓          ↓        ↓          ↓          ↓
 开始    数据可能存在?    缓存命中?   获取锁?   再次命中?   查询DB    设置缓存
  ↓           ↓              ↓          ↓        ↓          ↓          ↓
 继续      是/否(返回)     是(返回)   是/否    是(返回)   成功/失败   完成
```

## API接口说明

### 基础防护接口
- `GET /cache/breakdown-protection?key=xxx` - 基础缓存击穿防护
- `GET /cache/service-breakdown-protection?key=xxx` - 专业服务防护

### 布隆过滤器管理
- `GET /cache/batch-preload?keys=key1,key2,key3` - 批量预加载布隆过滤器
- `GET /cache/bloom-check?key=xxx` - 检查key是否在布隆过滤器中

### 测试和监控
- `GET /cache/breakdown-test?key=xxx` - 缓存击穿测试
- `GET /cache/stress-test?key=xxx&threads=10` - 压力测试
- `GET /cache/service-stats` - 获取详细统计信息
- `GET /cache/breakdown-stats` - 获取基础统计信息

### 管理接口
- `GET /cache/evict?key=xxx` - 强制删除缓存
- `GET /cache/cleanup-locks` - 清理无效锁
- `GET /cache/reset-stats` - 重置统计信息

## 使用示例

### 1. 预加载热点数据
```bash
curl "http://localhost:1000/cache/batch-preload?keys=user:1,user:2,product:1"
```

### 2. 测试缓存击穿防护
```bash
# 删除缓存模拟过期
curl "http://localhost:1000/cache/evict?key=user:100"

# 测试防护效果
curl "http://localhost:1000/cache/breakdown-protection?key=user:100"
```

### 3. 压力测试
```bash
curl "http://localhost:1000/cache/stress-test?key=hotkey&threads=20"
```

### 4. 监控统计
```bash
curl "http://localhost:1000/cache/service-stats"
```

## 性能优化建议

### 1. 布隆过滤器优化
- **合理设置容量**：根据实际数据量设置expectedInsertions
- **平衡误判率**：误判率越低，内存占用越大
- **定期重建**：避免误判率随时间增长

### 2. 锁策略优化
- **细粒度锁**：每个key使用独立的锁
- **锁超时设置**：避免死锁，设置合理的超时时间
- **及时清理**：释放锁后立即从映射中移除

### 3. 缓存策略优化
- **合理的过期时间**：避免频繁过期
- **空值缓存**：对不存在的数据也进行短期缓存
- **预热机制**：提前加载热点数据

## 监控指标

### 关键指标
- **布隆过滤器命中率**：衡量过滤效果
- **缓存命中率**：衡量缓存效果
- **锁等待次数**：衡量并发冲突
- **锁超时次数**：衡量系统压力
- **数据库查询次数**：衡量防护效果

### 告警阈值建议
- 布隆过滤器命中率 < 90%
- 缓存命中率 < 80%
- 锁超时次数 > 100/分钟
- 活跃锁数量 > 1000

## 注意事项

1. **内存使用**：布隆过滤器会占用一定内存，需要合理规划
2. **误判处理**：布隆过滤器可能误判，需要后续验证
3. **锁粒度**：使用key级别的锁，避免全局锁影响性能
4. **降级策略**：锁超时时应该有合理的降级方案
5. **监控告警**：建立完善的监控和告警机制
