package com.example.demo;

import com.example.performance.RedisPerformanceAnalyzer;
import com.example.performance.RedisMemoryOptimizer;
import com.example.performance.RedisNetworkOptimizer;
import com.example.performance.RedisCommandOptimizer;

/**
 * Redis性能优化演示
 * 综合展示Redis性能分析和优化策略
 */
public class PerformanceOptimizationDemo {
    
    private final String host;
    private final int port;
    private final String password;
    
    public PerformanceOptimizationDemo(String host, int port, String password) {
        this.host = host;
        this.port = port;
        this.password = password;
    }
    
    public PerformanceOptimizationDemo() {
        this("localhost", 6379, "");
    }
    
    /**
     * 运行完整的性能优化演示
     */
    public void runCompleteOptimization() {
        System.out.println("🚀 Redis性能优化综合演示");
        System.out.println("=" .repeat(100));
        
        try {
            // 1. 性能分析
            performanceAnalysis();
            
            // 2. 内存优化
            memoryOptimization();
            
            // 3. 网络优化
            networkOptimization();
            
            // 4. 命令优化
            commandOptimization();
            
            // 5. 生成综合报告
            generateComprehensiveReport();
            
        } catch (Exception e) {
            System.err.println("❌ 性能优化演示过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n✅ Redis性能优化演示完成！");
    }
    
    /**
     * 性能分析
     */
    private void performanceAnalysis() {
        System.out.println("\n" + "🔍 第一步：Redis性能分析".toUpperCase());
        System.out.println("=" .repeat(80));
        
        RedisPerformanceAnalyzer analyzer = new RedisPerformanceAnalyzer(host, port, password);
        
        try {
            analyzer.performCompleteAnalysis();
        } finally {
            analyzer.close();
        }
        
        waitForUserInput();
    }
    
    /**
     * 内存优化
     */
    private void memoryOptimization() {
        System.out.println("\n" + "💾 第二步：Redis内存优化".toUpperCase());
        System.out.println("=" .repeat(80));
        
        RedisNetworkOptimizer networkOptimizer = new RedisNetworkOptimizer(host, port, password);
        RedisMemoryOptimizer memoryOptimizer = new RedisMemoryOptimizer(networkOptimizer.getJedisPool());
        
        try {
            memoryOptimizer.performMemoryOptimization();
        } finally {
            networkOptimizer.close();
        }
        
        waitForUserInput();
    }
    
    /**
     * 网络优化
     */
    private void networkOptimization() {
        System.out.println("\n" + "🌐 第三步：Redis网络优化".toUpperCase());
        System.out.println("=" .repeat(80));
        
        RedisNetworkOptimizer networkOptimizer = new RedisNetworkOptimizer(host, port, password);
        
        try {
            networkOptimizer.performNetworkOptimization();
        } finally {
            networkOptimizer.close();
        }
        
        waitForUserInput();
    }
    
    /**
     * 命令优化
     */
    private void commandOptimization() {
        System.out.println("\n" + "⚡ 第四步：Redis命令优化".toUpperCase());
        System.out.println("=" .repeat(80));
        
        RedisNetworkOptimizer networkOptimizer = new RedisNetworkOptimizer(host, port, password);
        RedisCommandOptimizer commandOptimizer = new RedisCommandOptimizer(networkOptimizer.getJedisPool());
        
        try {
            commandOptimizer.performCommandOptimization();
        } finally {
            networkOptimizer.close();
        }
        
        waitForUserInput();
    }
    
    /**
     * 生成综合报告
     */
    private void generateComprehensiveReport() {
        System.out.println("\n" + "📊 第五步：综合优化报告".toUpperCase());
        System.out.println("=" .repeat(80));
        
        System.out.println("🎯 Redis性能优化综合建议");
        System.out.println("-" .repeat(50));
        
        System.out.println("\n📈 性能优化优先级:");
        System.out.println("1. 🔥 高优先级 - 立即处理");
        System.out.println("   - 清理过期键和无用数据");
        System.out.println("   - 优化慢查询命令");
        System.out.println("   - 修复内存碎片问题");
        System.out.println("   - 避免使用阻塞命令");
        
        System.out.println("\n2. 🟡 中优先级 - 计划处理");
        System.out.println("   - 优化数据结构选择");
        System.out.println("   - 实施批量操作");
        System.out.println("   - 调整连接池配置");
        System.out.println("   - 设置合理的过期时间");
        
        System.out.println("\n3. 🟢 低优先级 - 持续改进");
        System.out.println("   - 键命名规范化");
        System.out.println("   - 监控指标完善");
        System.out.println("   - 文档和培训");
        System.out.println("   - 定期性能评估");
        
        System.out.println("\n🛠️  实施建议:");
        System.out.println("1. 建立性能监控体系");
        System.out.println("   - 监控内存使用率");
        System.out.println("   - 监控慢查询日志");
        System.out.println("   - 监控连接数和网络延迟");
        System.out.println("   - 设置告警阈值");
        
        System.out.println("\n2. 制定优化计划");
        System.out.println("   - 分阶段实施优化");
        System.out.println("   - 测试验证效果");
        System.out.println("   - 记录优化过程");
        System.out.println("   - 定期回顾和调整");
        
        System.out.println("\n3. 团队培训和规范");
        System.out.println("   - Redis最佳实践培训");
        System.out.println("   - 代码审查规范");
        System.out.println("   - 性能测试流程");
        System.out.println("   - 问题排查手册");
        
        System.out.println("\n📋 配置优化建议:");
        generateConfigurationRecommendations();
        
        System.out.println("\n🔧 运维优化建议:");
        generateOperationalRecommendations();
        
        System.out.println("\n📚 学习资源推荐:");
        generateLearningResources();
    }
    
    /**
     * 生成配置优化建议
     */
    private void generateConfigurationRecommendations() {
        System.out.println("Redis配置优化:");
        System.out.println("  # 内存优化");
        System.out.println("  maxmemory 2gb");
        System.out.println("  maxmemory-policy allkeys-lru");
        System.out.println("  ");
        System.out.println("  # 持久化优化");
        System.out.println("  save 900 1");
        System.out.println("  save 300 10");
        System.out.println("  save 60 10000");
        System.out.println("  ");
        System.out.println("  # 网络优化");
        System.out.println("  tcp-keepalive 300");
        System.out.println("  timeout 0");
        System.out.println("  ");
        System.out.println("  # 慢查询优化");
        System.out.println("  slowlog-log-slower-than 10000");
        System.out.println("  slowlog-max-len 128");
        
        System.out.println("\nJVM优化 (如果使用Java客户端):");
        System.out.println("  -Xms2g -Xmx2g");
        System.out.println("  -XX:+UseG1GC");
        System.out.println("  -XX:MaxGCPauseMillis=200");
    }
    
    /**
     * 生成运维优化建议
     */
    private void generateOperationalRecommendations() {
        System.out.println("运维最佳实践:");
        System.out.println("1. 监控指标");
        System.out.println("   - 内存使用率 (<80%)");
        System.out.println("   - CPU使用率 (<70%)");
        System.out.println("   - 网络延迟 (<1ms)");
        System.out.println("   - 慢查询数量");
        System.out.println("   - 连接数");
        
        System.out.println("\n2. 备份策略");
        System.out.println("   - 定期RDB备份");
        System.out.println("   - AOF增量备份");
        System.out.println("   - 跨机房备份");
        System.out.println("   - 备份验证");
        
        System.out.println("\n3. 高可用部署");
        System.out.println("   - 主从复制");
        System.out.println("   - 哨兵模式");
        System.out.println("   - 集群模式");
        System.out.println("   - 故障自动切换");
        
        System.out.println("\n4. 安全措施");
        System.out.println("   - 密码认证");
        System.out.println("   - 网络隔离");
        System.out.println("   - 命令重命名");
        System.out.println("   - 访问控制");
    }
    
    /**
     * 生成学习资源推荐
     */
    private void generateLearningResources() {
        System.out.println("推荐学习资源:");
        System.out.println("1. 官方文档");
        System.out.println("   - Redis官方文档: https://redis.io/documentation");
        System.out.println("   - Redis命令参考: https://redis.io/commands");
        
        System.out.println("\n2. 性能优化指南");
        System.out.println("   - Redis性能调优指南");
        System.out.println("   - Redis内存优化最佳实践");
        System.out.println("   - Redis集群部署指南");
        
        System.out.println("\n3. 监控工具");
        System.out.println("   - Redis-cli info命令");
        System.out.println("   - RedisInsight可视化工具");
        System.out.println("   - Prometheus + Grafana监控");
        
        System.out.println("\n4. 社区资源");
        System.out.println("   - Redis中文社区");
        System.out.println("   - Stack Overflow");
        System.out.println("   - GitHub Redis项目");
    }
    
    /**
     * 等待用户输入继续
     */
    private void waitForUserInput() {
        System.out.println("\n⏸️  按Enter键继续下一步...");
        try {
            System.in.read();
        } catch (Exception e) {
            // 忽略输入异常
        }
    }
    
    /**
     * 快速性能检查
     */
    public void quickPerformanceCheck() {
        System.out.println("🔍 Redis快速性能检查");
        System.out.println("=" .repeat(60));
        
        RedisPerformanceAnalyzer analyzer = new RedisPerformanceAnalyzer(host, port, password);
        
        try {
            analyzer.analyzeBasicInfo();
            analyzer.analyzeMemoryUsage();
            analyzer.analyzeSlowLog();
            
            System.out.println("\n✅ 快速检查完成");
            System.out.println("💡 如需详细分析，请运行完整优化演示");
            
        } finally {
            analyzer.close();
        }
    }
    
    public static void main(String[] args) {
        PerformanceOptimizationDemo demo = new PerformanceOptimizationDemo();
        
        if (args.length > 0 && "quick".equals(args[0])) {
            demo.quickPerformanceCheck();
        } else {
            demo.runCompleteOptimization();
        }
    }
}
