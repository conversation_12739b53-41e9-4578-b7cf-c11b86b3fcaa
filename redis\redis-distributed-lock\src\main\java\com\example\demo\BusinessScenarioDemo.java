package com.example.demo;

import com.example.lock.DistributedLockManager;
import com.example.lock.RedisDistributedLock;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 业务场景演示
 * 展示分布式锁在实际业务中的应用
 */
public class BusinessScenarioDemo {
    
    private final DistributedLockManager lockManager;
    
    public BusinessScenarioDemo() {
        this.lockManager = new DistributedLockManager("localhost", 6379, "");
    }
    
    /**
     * 场景1：秒杀活动
     */
    public void demonstrateSeckillActivity() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("⚡ 场景1：秒杀活动 - 防止超卖");
        System.out.println("=" .repeat(60));
        
        String productId = "seckill_product_001";
        int totalStock = 10; // 总库存10件
        int userCount = 50;  // 50个用户参与秒杀
        
        // 初始化商品库存
        try (var jedis = lockManager.getJedisPool().getResource()) {
            jedis.set("seckill:stock:" + productId, String.valueOf(totalStock));
            jedis.set("seckill:sold:" + productId, "0");
            System.out.println("🛍️  秒杀商品初始化: " + productId + ", 库存: " + totalStock);
        }
        
        ExecutorService executor = Executors.newFixedThreadPool(userCount);
        CountDownLatch latch = new CountDownLatch(userCount);
        
        System.out.println("🚀 " + userCount + " 个用户同时参与秒杀...");
        
        for (int i = 1; i <= userCount; i++) {
            final int userId = i;
            executor.submit(() -> {
                try {
                    boolean success = seckillProduct(productId, userId);
                    if (success) {
                        System.out.println("🎉 用户" + userId + " 秒杀成功！");
                    } else {
                        System.out.println("😢 用户" + userId + " 秒杀失败");
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 统计结果
        try (var jedis = lockManager.getJedisPool().getResource()) {
            String remainingStock = jedis.get("seckill:stock:" + productId);
            String soldCount = jedis.get("seckill:sold:" + productId);
            System.out.println("📊 秒杀结果统计:");
            System.out.println("   剩余库存: " + remainingStock);
            System.out.println("   已售数量: " + soldCount);
            System.out.println("   总库存: " + totalStock);
        }
        
        executor.shutdown();
    }
    
    /**
     * 秒杀商品
     */
    private boolean seckillProduct(String productId, int userId) {
        String lockKey = "seckill:lock:" + productId;
        RedisDistributedLock lock = lockManager.createLock(lockKey, 10);
        
        try {
            if (lock.tryLock(2, TimeUnit.SECONDS)) {
                try (var jedis = lockManager.getJedisPool().getResource()) {
                    // 检查库存
                    String stockStr = jedis.get("seckill:stock:" + productId);
                    int currentStock = Integer.parseInt(stockStr);
                    
                    if (currentStock > 0) {
                        // 模拟业务处理时间
                        Thread.sleep(50);
                        
                        // 扣减库存
                        jedis.set("seckill:stock:" + productId, String.valueOf(currentStock - 1));
                        
                        // 增加销售数量
                        jedis.incr("seckill:sold:" + productId);
                        
                        // 记录订单
                        String orderId = "ORDER_" + productId + "_" + userId + "_" + System.currentTimeMillis();
                        jedis.set("order:" + orderId, "userId:" + userId + ",productId:" + productId + ",time:" + 
                                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        
                        return true;
                    } else {
                        return false; // 库存不足
                    }
                }
            } else {
                return false; // 获取锁超时
            }
        } catch (Exception e) {
            System.err.println("❌ 用户" + userId + " 秒杀异常: " + e.getMessage());
            return false;
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 场景2：账户转账
     */
    public void demonstrateAccountTransfer() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("💰 场景2：账户转账 - 防止并发转账问题");
        System.out.println("=" .repeat(60));
        
        // 初始化账户余额
        String accountA = "account:1001";
        String accountB = "account:1002";
        BigDecimal initialBalanceA = new BigDecimal("1000.00");
        BigDecimal initialBalanceB = new BigDecimal("500.00");
        
        try (var jedis = lockManager.getJedisPool().getResource()) {
            jedis.set(accountA, initialBalanceA.toString());
            jedis.set(accountB, initialBalanceB.toString());
            System.out.println("💳 账户初始化:");
            System.out.println("   账户A: " + initialBalanceA);
            System.out.println("   账户B: " + initialBalanceB);
        }
        
        // 模拟多个并发转账
        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(10);
        
        System.out.println("🔄 开始并发转账测试...");
        
        for (int i = 1; i <= 10; i++) {
            final int transferId = i;
            executor.submit(() -> {
                try {
                    BigDecimal amount = new BigDecimal("50.00");
                    boolean success = transferMoney(accountA, accountB, amount, transferId);
                    if (success) {
                        System.out.println("✅ 转账" + transferId + " 成功: " + amount);
                    } else {
                        System.out.println("❌ 转账" + transferId + " 失败");
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查最终余额
        try (var jedis = lockManager.getJedisPool().getResource()) {
            String finalBalanceA = jedis.get(accountA);
            String finalBalanceB = jedis.get(accountB);
            System.out.println("📊 转账后余额:");
            System.out.println("   账户A: " + finalBalanceA);
            System.out.println("   账户B: " + finalBalanceB);
            
            BigDecimal totalBefore = initialBalanceA.add(initialBalanceB);
            BigDecimal totalAfter = new BigDecimal(finalBalanceA).add(new BigDecimal(finalBalanceB));
            System.out.println("   总金额变化: " + totalBefore + " -> " + totalAfter);
        }
        
        executor.shutdown();
    }
    
    /**
     * 转账操作
     */
    private boolean transferMoney(String fromAccount, String toAccount, BigDecimal amount, int transferId) {
        // 使用账户ID排序来避免死锁
        String lockKey1 = "transfer:lock:" + (fromAccount.compareTo(toAccount) < 0 ? fromAccount : toAccount);
        String lockKey2 = "transfer:lock:" + (fromAccount.compareTo(toAccount) < 0 ? toAccount : fromAccount);
        
        RedisDistributedLock lock1 = lockManager.createLock(lockKey1, 10);
        RedisDistributedLock lock2 = lockManager.createLock(lockKey2, 10);
        
        try {
            // 按顺序获取锁，避免死锁
            if (lock1.tryLock(3, TimeUnit.SECONDS)) {
                if (lock2.tryLock(3, TimeUnit.SECONDS)) {
                    try (var jedis = lockManager.getJedisPool().getResource()) {
                        // 检查余额
                        String fromBalanceStr = jedis.get(fromAccount);
                        BigDecimal fromBalance = new BigDecimal(fromBalanceStr);
                        
                        if (fromBalance.compareTo(amount) >= 0) {
                            // 模拟转账处理时间
                            Thread.sleep(100);
                            
                            // 扣减转出账户
                            BigDecimal newFromBalance = fromBalance.subtract(amount);
                            jedis.set(fromAccount, newFromBalance.toString());
                            
                            // 增加转入账户
                            String toBalanceStr = jedis.get(toAccount);
                            BigDecimal toBalance = new BigDecimal(toBalanceStr);
                            BigDecimal newToBalance = toBalance.add(amount);
                            jedis.set(toAccount, newToBalance.toString());
                            
                            // 记录转账记录
                            String transferRecord = String.format("转账%d: %s -> %s, 金额: %s, 时间: %s",
                                    transferId, fromAccount, toAccount, amount,
                                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS")));
                            jedis.lpush("transfer:records", transferRecord);
                            
                            return true;
                        } else {
                            System.out.println("⚠️  转账" + transferId + " 余额不足: " + fromBalance + " < " + amount);
                            return false;
                        }
                    }
                } else {
                    System.out.println("⏰ 转账" + transferId + " 获取第二个锁超时");
                    return false;
                }
            } else {
                System.out.println("⏰ 转账" + transferId + " 获取第一个锁超时");
                return false;
            }
        } catch (Exception e) {
            System.err.println("❌ 转账" + transferId + " 异常: " + e.getMessage());
            return false;
        } finally {
            lock2.unlock();
            lock1.unlock();
        }
    }
    
    /**
     * 场景3：分布式任务调度
     */
    public void demonstrateDistributedTaskScheduling() {
        System.out.println("\n" + "=" .repeat(60));
        System.out.println("⏰ 场景3：分布式任务调度 - 确保任务只执行一次");
        System.out.println("=" .repeat(60));
        
        String taskId = "daily_report_task";
        int nodeCount = 5; // 模拟5个节点
        
        ExecutorService executor = Executors.newFixedThreadPool(nodeCount);
        CountDownLatch latch = new CountDownLatch(nodeCount);
        
        System.out.println("🖥️  " + nodeCount + " 个节点同时尝试执行定时任务...");
        
        for (int i = 1; i <= nodeCount; i++) {
            final int nodeId = i;
            executor.submit(() -> {
                try {
                    executeScheduledTask(taskId, nodeId);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(20, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
    }
    
    /**
     * 执行定时任务
     */
    private void executeScheduledTask(String taskId, int nodeId) {
        String lockKey = "task:lock:" + taskId;
        RedisDistributedLock lock = lockManager.createLock(lockKey, 60); // 任务锁定60秒
        
        try {
            if (lock.tryLock(1, TimeUnit.SECONDS)) {
                System.out.println("🎯 节点" + nodeId + " 获得任务执行权: " + taskId);
                
                // 检查任务是否已经执行过
                try (var jedis = lockManager.getJedisPool().getResource()) {
                    String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String taskKey = "task:executed:" + taskId + ":" + today;
                    
                    if (jedis.exists(taskKey)) {
                        System.out.println("ℹ️  节点" + nodeId + " 发现任务今日已执行，跳过");
                        return;
                    }
                    
                    // 执行任务
                    System.out.println("🔄 节点" + nodeId + " 开始执行任务: " + taskId);
                    
                    // 模拟任务执行时间
                    for (int i = 1; i <= 5; i++) {
                        Thread.sleep(1000);
                        System.out.println("   节点" + nodeId + " 任务进度: " + (i * 20) + "%");
                    }
                    
                    // 标记任务已完成
                    jedis.setex(taskKey, 24 * 60 * 60, "executed_by_node_" + nodeId); // 24小时过期
                    
                    System.out.println("✅ 节点" + nodeId + " 任务执行完成: " + taskId);
                }
            } else {
                System.out.println("⏰ 节点" + nodeId + " 获取任务锁超时，任务可能正在执行");
            }
        } catch (Exception e) {
            System.err.println("❌ 节点" + nodeId + " 执行任务异常: " + e.getMessage());
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 运行所有业务场景演示
     */
    public void runAllScenarios() {
        System.out.println("🚀 开始业务场景演示");
        
        try {
            demonstrateSeckillActivity();
            demonstrateAccountTransfer();
            demonstrateDistributedTaskScheduling();
            
        } catch (Exception e) {
            System.err.println("❌ 演示过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            lockManager.shutdown();
        }
        
        System.out.println("\n✅ 所有业务场景演示完成！");
    }
    
    public static void main(String[] args) {
        BusinessScenarioDemo demo = new BusinessScenarioDemo();
        demo.runAllScenarios();
    }
}
