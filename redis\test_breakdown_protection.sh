#!/bin/bash

# 缓存击穿防护测试脚本
# 确保Spring Boot应用已启动在localhost:1000

BASE_URL="http://localhost:1000"

echo "=== 缓存击穿防护机制测试 ==="
echo

# 1. 批量预加载布隆过滤器
echo "1. 批量预加载布隆过滤器..."
curl -s "${BASE_URL}/cache/batch-preload?keys=user:1,user:2,user:3,product:1,product:2,product:3"
echo -e "\n"

# 2. 检查布隆过滤器
echo "2. 检查布隆过滤器..."
echo "2.1 检查存在的key:"
curl -s "${BASE_URL}/cache/bloom-check?key=user:1"
echo -e "\n"

echo "2.2 检查不存在的key:"
curl -s "${BASE_URL}/cache/bloom-check?key=user:999999"
echo -e "\n"

# 3. 测试基础缓存击穿防护
echo "3. 测试基础缓存击穿防护..."
curl -s "${BASE_URL}/cache/breakdown-protection?key=user:100"
echo -e "\n"

# 4. 测试专业服务的缓存击穿防护
echo "4. 测试专业服务的缓存击穿防护..."
curl -s "${BASE_URL}/cache/service-breakdown-protection?key=user:200"
echo -e "\n"

# 5. 查看统计信息
echo "5. 查看统计信息..."
curl -s "${BASE_URL}/cache/service-stats"
echo -e "\n"

# 6. 测试缓存击穿场景
echo "6. 测试缓存击穿场景..."
echo "6.1 删除缓存模拟过期:"
curl -s "${BASE_URL}/cache/evict?key=user:300"
echo -e "\n"

echo "6.2 高并发访问测试:"
curl -s "${BASE_URL}/cache/breakdown-test?key=user:300"
echo -e "\n"

# 7. 压力测试
echo "7. 压力测试（10个并发线程）..."
curl -s "${BASE_URL}/cache/stress-test?key=user:stress&threads=10"
echo -e "\n"

# 8. 再次查看统计信息
echo "8. 测试后统计信息..."
curl -s "${BASE_URL}/cache/service-stats"
echo -e "\n"

# 9. 清理锁
echo "9. 清理无效锁..."
curl -s "${BASE_URL}/cache/cleanup-locks"
echo -e "\n"

# 10. 获取基础统计
echo "10. 获取基础统计信息..."
curl -s "${BASE_URL}/cache/breakdown-stats"
echo -e "\n"

# 11. 重置统计信息
echo "11. 重置统计信息..."
curl -s "${BASE_URL}/cache/reset-stats"
echo -e "\n"

echo "=== 测试完成 ==="

echo
echo "=== 测试说明 ==="
echo "1. 布隆过滤器：快速判断数据是否可能存在，避免无效的数据库查询"
echo "2. 互斥锁：防止多个线程同时重建缓存，避免数据库压力"
echo "3. 双重检查：获取锁后再次检查缓存，提高效率"
echo "4. 空值缓存：对不存在的数据也进行短期缓存，避免频繁查询"
echo "5. 统计监控：提供详细的性能统计信息，便于监控和优化"
