package com.example.test;

import com.example.lock.DistributedLockManager;
import com.example.lock.RedisDistributedLock;
import com.example.lock.ReentrantRedisLock;

import java.util.concurrent.TimeUnit;

/**
 * 分布式锁测试类
 * 简单测试分布式锁的基本功能
 */
public class LockTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 Redis分布式锁功能测试");
        System.out.println("=" .repeat(50));
        
        DistributedLockManager lockManager = new DistributedLockManager("localhost", 6379, "");
        
        try {
            testBasicLock(lockManager);
            testReentrantLock(lockManager);
            testLockWithTimeout(lockManager);
            testLockRenewal(lockManager);
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            lockManager.shutdown();
        }
        
        System.out.println("\n✅ 所有测试完成！");
    }
    
    /**
     * 测试基本锁功能
     */
    private static void testBasicLock(DistributedLockManager lockManager) {
        System.out.println("\n🔒 测试1：基本锁功能");
        System.out.println("-" .repeat(30));
        
        RedisDistributedLock lock = lockManager.createLock("test_basic_lock", 10);
        
        // 测试获取锁
        if (lock.tryLock()) {
            System.out.println("✅ 获取锁成功");
            System.out.println("📊 锁信息: " + lock.getLockInfo());
            System.out.println("⏰ 锁TTL: " + lock.getTimeToLive() + "秒");
            
            // 测试重复获取锁（应该失败）
            RedisDistributedLock lock2 = lockManager.createLock("test_basic_lock", 10);
            if (!lock2.tryLock()) {
                System.out.println("✅ 重复获取锁失败（符合预期）");
            }
            
            // 释放锁
            if (lock.unlock()) {
                System.out.println("✅ 释放锁成功");
            }
            
            // 再次尝试获取锁（应该成功）
            if (lock2.tryLock()) {
                System.out.println("✅ 锁释放后重新获取成功");
                lock2.unlock();
            }
        } else {
            System.out.println("❌ 获取锁失败");
        }
    }
    
    /**
     * 测试可重入锁
     */
    private static void testReentrantLock(DistributedLockManager lockManager) {
        System.out.println("\n🔄 测试2：可重入锁功能");
        System.out.println("-" .repeat(30));
        
        ReentrantRedisLock lock = lockManager.createReentrantLock("test_reentrant_lock", 15);
        
        // 第一次获取锁
        if (lock.tryLock()) {
            System.out.println("✅ 第一次获取锁成功，重入次数: " + lock.getReentrantCount());
            
            // 第二次获取锁（可重入）
            if (lock.tryLock()) {
                System.out.println("✅ 第二次获取锁成功，重入次数: " + lock.getReentrantCount());
                
                // 第三次获取锁（可重入）
                if (lock.tryLock()) {
                    System.out.println("✅ 第三次获取锁成功，重入次数: " + lock.getReentrantCount());
                    
                    // 释放锁（需要释放3次）
                    lock.unlock();
                    System.out.println("🔓 第一次释放，剩余重入次数: " + lock.getReentrantCount());
                    
                    lock.unlock();
                    System.out.println("🔓 第二次释放，剩余重入次数: " + lock.getReentrantCount());
                    
                    lock.unlock();
                    System.out.println("🔓 第三次释放，剩余重入次数: " + lock.getReentrantCount());
                }
            }
        }
    }
    
    /**
     * 测试带超时的锁
     */
    private static void testLockWithTimeout(DistributedLockManager lockManager) {
        System.out.println("\n⏰ 测试3：带超时的锁获取");
        System.out.println("-" .repeat(30));
        
        RedisDistributedLock lock1 = lockManager.createLock("test_timeout_lock", 5);
        RedisDistributedLock lock2 = lockManager.createLock("test_timeout_lock", 5);
        
        // 第一个锁获取成功
        if (lock1.tryLock()) {
            System.out.println("✅ 第一个锁获取成功");
            
            // 第二个锁尝试获取（带超时）
            long startTime = System.currentTimeMillis();
            boolean acquired = lock2.tryLock(3, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            
            if (!acquired) {
                System.out.println("✅ 第二个锁获取超时（符合预期），耗时: " + (endTime - startTime) + "ms");
            } else {
                System.out.println("❌ 第二个锁不应该获取成功");
            }
            
            lock1.unlock();
            System.out.println("🔓 第一个锁已释放");
            
            // 现在第二个锁应该能获取成功
            if (lock2.tryLock(1, TimeUnit.SECONDS)) {
                System.out.println("✅ 第一个锁释放后，第二个锁获取成功");
                lock2.unlock();
            }
        }
    }
    
    /**
     * 测试锁续期
     */
    private static void testLockRenewal(DistributedLockManager lockManager) {
        System.out.println("\n🔄 测试4：锁续期功能");
        System.out.println("-" .repeat(30));
        
        RedisDistributedLock lock = lockManager.createLock("test_renewal_lock", 3); // 3秒过期
        
        if (lock.tryLock()) {
            System.out.println("✅ 获取锁成功");
            System.out.println("📊 初始TTL: " + lock.getTimeToLive() + "秒");
            
            try {
                // 等待2秒
                Thread.sleep(2000);
                System.out.println("📊 2秒后TTL: " + lock.getTimeToLive() + "秒");
                
                // 续期到10秒
                if (lock.renewLock(10)) {
                    System.out.println("✅ 锁续期成功");
                    System.out.println("📊 续期后TTL: " + lock.getTimeToLive() + "秒");
                } else {
                    System.out.println("❌ 锁续期失败");
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                lock.unlock();
                System.out.println("🔓 锁已释放");
            }
        }
    }
}
