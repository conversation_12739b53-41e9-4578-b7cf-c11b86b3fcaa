<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="57ebac49-6f10-447b-9315-2ef35b37cad5" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="$PROJECT_DIR$/../../../yjssi/apache-maven-3.9.9" />
        <option name="userSettingsFile" value="D:\yjssi\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="workspaceImportEnabled" value="true" />
      </MavenImportingSettings>
    </option>
    <option name="disabledProfiles">
      <list>
        <option value="jdk17" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="30uidnQNDYQm3X7IZMbaKRT2H3L" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "jdk.selected.JAVA_MODULE": "1.8",
    "last_opened_file_path": "D:/zhuangaowu-javafile/jifen",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "settings.editor.selected.configurable": "vcs.Git",
    "spring.configuration.checksum": "5fc0991e0adeec00afefcce6f77d9b10",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="应用程序.SingleRedisDemoFixed">
    <configuration name="ClusterExample" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zhentao.config.ClusterExample" />
      <module name="redis" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FixSingleRedis" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zhentao.config.FixSingleRedis" />
      <module name="redis" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MasterSlaveExample" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zhentao.config.MasterSlaveExample" />
      <module name="redis" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SingleRedisDemo" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zhentao.config.SingleRedisDemo" />
      <module name="redis" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SingleRedisDemoFixed" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zhentao.config.SingleRedisDemoFixed" />
      <module name="redis" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.SingleRedisDemoFixed" />
        <item itemvalue="应用程序.FixSingleRedis" />
        <item itemvalue="应用程序.SingleRedisDemo" />
        <item itemvalue="应用程序.MasterSlaveExample" />
        <item itemvalue="应用程序.ClusterExample" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="57ebac49-6f10-447b-9315-2ef35b37cad5" name="更改" comment="" />
      <created>1754483949493</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754483949493</updated>
      <workItem from="1754483951080" duration="2531000" />
      <workItem from="1754533237908" duration="9639000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>