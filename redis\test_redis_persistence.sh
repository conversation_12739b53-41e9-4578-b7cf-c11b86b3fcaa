#!/bin/bash

# Redis持久化配置测试脚本
# 确保Spring Boot应用已启动在localhost:1000

BASE_URL="http://localhost:1000"

echo "=== Redis持久化配置测试与查看 ==="
echo

# 1. 配置RDB持久化
echo "1. 配置RDB持久化..."
echo "1.1 设置RDB保存策略（900秒内1个变化）:"
curl -s "${BASE_URL}/RDB?saveSeconds=900&changedKeys=1"
echo -e "\n"

echo "1.2 设置RDB保存策略（300秒内10个变化）:"
curl -s "${BASE_URL}/RDB?saveSeconds=300&changedKeys=10"
echo -e "\n"

# 2. 配置AOF持久化
echo "2. 配置AOF持久化..."
echo "2.1 启用AOF，设置每秒同步:"
curl -s "${BASE_URL}/AOF?enable=true&fsync=everysec"
echo -e "\n"

# 3. 查看RDB配置
echo "3. 查看RDB配置..."
curl -s "${BASE_URL}/redis/rdb/config"
echo -e "\n"

# 4. 查看AOF配置
echo "4. 查看AOF配置..."
curl -s "${BASE_URL}/redis/aof/config"
echo -e "\n"

# 5. 查看持久化状态
echo "5. 查看持久化状态..."
curl -s "${BASE_URL}/redis/status"
echo -e "\n"

# 6. 查看详细统计信息
echo "6. 查看详细统计信息..."
curl -s "${BASE_URL}/redis/stats"
echo -e "\n"

# 7. 手动触发RDB保存
echo "7. 手动触发RDB保存..."
curl -s "${BASE_URL}/RDB/save?async=true"
echo -e "\n"

# 8. 手动触发AOF重写
echo "8. 手动触发AOF重写..."
curl -s "${BASE_URL}/AOP/restart"
echo -e "\n"

# 9. 查看所有持久化配置
echo "9. 查看所有持久化配置..."
curl -s "${BASE_URL}/redis/persistence/all"
echo -e "\n"

# 10. 查看特定配置模式
echo "10. 查看特定配置..."
echo "10.1 查看save相关配置:"
curl -s "${BASE_URL}/redis/config?pattern=*save*"
echo -e "\n"

echo "10.2 查看aof相关配置:"
curl -s "${BASE_URL}/redis/config?pattern=*aof*"
echo -e "\n"

echo "=== 测试完成 ==="

echo
echo "=== 查看配置的方法总结 ==="
echo "1. 基础查看接口:"
echo "   - GET /redis/status           - 查看持久化状态"
echo "   - GET /redis/stats            - 查看详细统计信息"
echo "   - GET /redis/config           - 查看所有配置"
echo
echo "2. 专项查看接口:"
echo "   - GET /redis/rdb/config       - 查看RDB配置"
echo "   - GET /redis/aof/config       - 查看AOF配置"
echo "   - GET /redis/persistence/all  - 查看完整持久化配置"
echo
echo "3. 模式查看接口:"
echo "   - GET /redis/config?pattern=*save*   - 查看save相关配置"
echo "   - GET /redis/config?pattern=*aof*    - 查看AOF相关配置"
echo "   - GET /redis/config?pattern=*rdb*    - 查看RDB相关配置"
echo
echo "4. 操作接口:"
echo "   - GET /RDB?saveSeconds=900&changedKeys=1  - 配置RDB"
echo "   - GET /AOF?enable=true&fsync=everysec     - 配置AOF"
echo "   - GET /RDB/save?async=true                - 手动RDB保存"
echo "   - GET /AOP/restart                        - 手动AOF重写"
