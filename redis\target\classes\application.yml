server:
  port: 1000

spring:
  redis:
    host: ***************
    port: 6379
    password: root
    timeout: 3000ms
    database: 0

    # 连接池配置
    lettuce:
      pool:
        max-active: 20    # 最大连接数
        max-idle: 10      # 最大空闲连接数
        min-idle: 5       # 最小空闲连接数
        max-wait: 3000ms  # 最大等待时间

# 缓存策略配置
cache:
  strategy:
    # 布隆过滤器配置
    bloom-filter:
      expected-insertions: 1000000  # 预期插入元素数量
      false-positive-probability: 0.01  # 误判率

    # 缓存过期时间配置
    expire:
      base-seconds: 300      # 基础过期时间（秒）
      random-seconds: 60     # 随机过期时间范围（秒）
      null-value-seconds: 60 # 空值缓存时间（秒）

    # 锁配置
    lock:
      timeout-seconds: 10           # 互斥锁超时时间
      comprehensive-timeout: 5      # 综合防护锁超时时间

    # 降级配置
    degrade:
      auto-enable-threshold: 100    # 自动开启降级的错误阈值

# 日志配置
logging:
  level:
    com.zhentao: DEBUG
    org.springframework.data.redis: DEBUG