# Redis分布式锁实现

## 概述

本项目提供了完整的Redis分布式锁解决方案，用于解决分布式系统中的并发控制和数据一致性问题。

## 项目结构

```
redis-distributed-lock/
├── src/main/java/com/example/
│   ├── lock/
│   │   ├── RedisDistributedLock.java      # 基础分布式锁
│   │   ├── ReentrantRedisLock.java        # 可重入分布式锁
│   │   └── DistributedLockManager.java    # 锁管理器
│   ├── demo/
│   │   ├── ConcurrencyDemo.java           # 并发场景演示
│   │   └── BusinessScenarioDemo.java      # 业务场景演示
│   └── test/
│       └── LockTest.java                  # 功能测试
├── pom.xml                                # Maven配置
└── README.md                              # 使用说明
```

## 核心特性

### 🔒 基础分布式锁 (RedisDistributedLock)
- ✅ 基于Redis SET命令的原子性操作
- ✅ 自动过期机制，防止死锁
- ✅ 使用Lua脚本确保操作原子性
- ✅ 支持锁续期功能
- ✅ 支持超时获取锁

### 🔄 可重入分布式锁 (ReentrantRedisLock)
- ✅ 支持同一线程多次获取同一把锁
- ✅ 基于Hash结构存储线程ID和重入次数
- ✅ 自动管理重入计数
- ✅ 安全的锁释放机制

### 🛠️ 锁管理器 (DistributedLockManager)
- ✅ 统一的锁创建和管理
- ✅ 自动续期服务
- ✅ 批量锁操作
- ✅ 资源自动清理

## 快速开始

### 1. 环境要求
- Java 8+
- Maven 3.6+
- Redis 6.0+

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行演示

#### 并发场景演示
```bash
mvn exec:java -P concurrency-demo
```

#### 业务场景演示
```bash
mvn exec:java -P business-demo
```

#### 功能测试
```bash
mvn exec:java -P lock-test
```

## 使用示例

### 基础分布式锁

```java
// 创建锁管理器
DistributedLockManager lockManager = new DistributedLockManager("localhost", 6379, "password");

// 创建分布式锁
RedisDistributedLock lock = lockManager.createLock("my_lock", 30); // 30秒过期

try {
    if (lock.tryLock(5, TimeUnit.SECONDS)) { // 5秒超时
        // 执行业务逻辑
        System.out.println("获取锁成功，执行业务逻辑");
        
        // 可以续期锁
        lock.renewLock(60); // 续期到60秒
        
    } else {
        System.out.println("获取锁失败");
    }
} finally {
    lock.unlock(); // 释放锁
}
```

### 可重入分布式锁

```java
ReentrantRedisLock reentrantLock = lockManager.createReentrantLock("reentrant_lock", 30);

public void method1() {
    if (reentrantLock.tryLock()) {
        try {
            System.out.println("方法1获取锁，重入次数: " + reentrantLock.getReentrantCount());
            method2(); // 调用另一个需要同一把锁的方法
        } finally {
            reentrantLock.unlock();
        }
    }
}

public void method2() {
    if (reentrantLock.tryLock()) { // 可重入
        try {
            System.out.println("方法2获取锁，重入次数: " + reentrantLock.getReentrantCount());
            // 业务逻辑
        } finally {
            reentrantLock.unlock();
        }
    }
}
```

### 使用锁管理器简化操作

```java
// 执行带锁的操作
String result = lockManager.executeWithLock("business_lock", 10, () -> {
    // 业务逻辑
    return "执行结果";
});

// 执行带可重入锁的操作
String result2 = lockManager.executeWithReentrantLock("reentrant_business_lock", 10, () -> {
    // 业务逻辑
    return "执行结果";
});
```

## 应用场景

### 1. 库存扣减
```java
public boolean deductStock(String productId, int quantity) {
    RedisDistributedLock lock = lockManager.createLock("stock:" + productId, 10);
    
    try {
        if (lock.tryLock(5, TimeUnit.SECONDS)) {
            // 检查库存
            int currentStock = getCurrentStock(productId);
            if (currentStock >= quantity) {
                // 扣减库存
                updateStock(productId, currentStock - quantity);
                return true;
            }
            return false;
        }
        return false;
    } finally {
        lock.unlock();
    }
}
```

### 2. 秒杀活动
```java
public boolean seckill(String productId, String userId) {
    String lockKey = "seckill:" + productId;
    return lockManager.executeWithLock(lockKey, 3, () -> {
        // 检查库存
        if (hasStock(productId)) {
            // 创建订单
            createOrder(productId, userId);
            // 扣减库存
            deductStock(productId, 1);
            return true;
        }
        return false;
    });
}
```

### 3. 分布式任务调度
```java
public void executeScheduledTask(String taskId) {
    String lockKey = "task:" + taskId;
    RedisDistributedLock lock = lockManager.createLock(lockKey, 3600); // 1小时
    
    try {
        if (lock.tryLock(1, TimeUnit.SECONDS)) {
            // 检查任务是否已执行
            if (!isTaskExecutedToday(taskId)) {
                // 执行任务
                doTask(taskId);
                // 标记任务已执行
                markTaskExecuted(taskId);
            }
        }
    } finally {
        lock.unlock();
    }
}
```

### 4. 账户转账
```java
public boolean transfer(String fromAccount, String toAccount, BigDecimal amount) {
    // 按账户ID排序获取锁，避免死锁
    String lockKey1 = "account:" + (fromAccount.compareTo(toAccount) < 0 ? fromAccount : toAccount);
    String lockKey2 = "account:" + (fromAccount.compareTo(toAccount) < 0 ? toAccount : fromAccount);
    
    RedisDistributedLock lock1 = lockManager.createLock(lockKey1, 30);
    RedisDistributedLock lock2 = lockManager.createLock(lockKey2, 30);
    
    try {
        if (lock1.tryLock(5, TimeUnit.SECONDS) && lock2.tryLock(5, TimeUnit.SECONDS)) {
            // 检查余额
            if (getBalance(fromAccount).compareTo(amount) >= 0) {
                // 执行转账
                deductBalance(fromAccount, amount);
                addBalance(toAccount, amount);
                return true;
            }
        }
        return false;
    } finally {
        lock2.unlock();
        lock1.unlock();
    }
}
```

## 技术实现

### 1. 基础锁实现原理
```lua
-- 加锁Lua脚本
if redis.call('set', KEYS[1], ARGV[1], 'NX', 'EX', ARGV[2]) then
    return 1
else
    return 0
end

-- 解锁Lua脚本
if redis.call('get', KEYS[1]) == ARGV[1] then
    return redis.call('del', KEYS[1])
else
    return 0
end
```

### 2. 可重入锁实现原理
```lua
-- 可重入加锁
local key = KEYS[1]
local threadId = ARGV[1]
local expireTime = ARGV[2]
local lockValue = redis.call('hget', key, 'threadId')

if lockValue == false then
    redis.call('hset', key, 'threadId', threadId)
    redis.call('hset', key, 'count', 1)
    redis.call('expire', key, expireTime)
    return 1
elseif lockValue == threadId then
    local count = redis.call('hget', key, 'count')
    redis.call('hset', key, 'count', count + 1)
    redis.call('expire', key, expireTime)
    return 1
else
    return 0
end
```

## 最佳实践

### 1. 锁的粒度
- ✅ 尽量使用细粒度的锁
- ✅ 避免大范围的锁竞争
- ✅ 根据业务场景选择合适的锁key

### 2. 超时设置
- ✅ 设置合理的锁过期时间
- ✅ 设置合理的获取锁超时时间
- ✅ 对于长时间任务，使用自动续期

### 3. 异常处理
- ✅ 始终在finally块中释放锁
- ✅ 处理获取锁失败的情况
- ✅ 记录锁操作的日志

### 4. 性能优化
- ✅ 使用连接池管理Redis连接
- ✅ 合理设置连接池参数
- ✅ 监控锁的使用情况

## 注意事项

1. **Redis高可用**: 生产环境建议使用Redis集群或哨兵模式
2. **网络分区**: 考虑网络分区对锁的影响
3. **时钟同步**: 确保各节点时钟同步
4. **锁续期**: 对于长时间任务，及时续期避免锁过期
5. **死锁避免**: 多个锁时按固定顺序获取

## 监控和运维

### 1. 锁状态监控
```java
// 获取所有锁状态
String status = lockManager.getAllLockStatus();
System.out.println(status);
```

### 2. 锁清理
```java
// 清理所有锁（谨慎使用）
lockManager.cleanupAllLocks();
```

### 3. 资源释放
```java
// 关闭锁管理器
lockManager.shutdown();
```

## 扩展功能

项目还可以扩展以下功能：
1. 读写锁实现
2. 公平锁实现
3. 锁监控面板
4. 锁性能统计
5. 分布式信号量

## 总结

本项目提供了完整的Redis分布式锁解决方案，涵盖了从基础实现到实际应用的各个方面。通过合理使用分布式锁，可以有效解决分布式系统中的并发控制问题，确保数据一致性和业务正确性。
