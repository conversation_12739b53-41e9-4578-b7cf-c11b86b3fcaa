package com.zhentao.config;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 单Redis实例演示三种架构 - 自动修复版本
 * 自动检测并修复只读问题，然后演示三种Redis架构
 */
public class SingleRedisDemoFixed {
    
    private JedisPool jedisPool;
    private Random random = new Random();
    
    // 模拟主从架构的数据结构
    private Map<String, String> masterData = new ConcurrentHashMap<>();
    private Map<String, String> slaveData = new ConcurrentHashMap<>();
    
    // 模拟哨兵状态
    private boolean masterAvailable = true;
    private String currentMaster = "redis-master";
    
    // 模拟集群槽位分配
    private Map<Integer, String> slotToNode = new HashMap<>();
    
    public SingleRedisDemoFixed() {
        System.out.println("🚀 启动单Redis演示（自动修复版本）");
//        System.out.println("=" .repeat(50));
        
        if (initializeRedisConnection()) {
            initializeClusterSlots();
            System.out.println("✅ 初始化完成，准备开始演示\n");
        } else {
            throw new RuntimeException("Redis初始化失败");
        }
    }
    
    /**
     * 初始化Redis连接并自动修复只读问题
     */
    private boolean initializeRedisConnection() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(10);
        config.setMaxIdle(5);
        config.setMinIdle(2);
        config.setTestOnBorrow(true);
        
        // 候选连接配置
        String[][] connectionConfigs = {
            {"***************", "6379", "root"},
        };
        
        for (String[] configItem : connectionConfigs) {
            String host = configItem[0];
            int port = Integer.parseInt(configItem[1]);
            String password = configItem[2];
            
            System.out.println("🔗 尝试连接: " + host + ":" + port + 
                             (password.isEmpty() ? "" : " (使用密码)"));
            
            try {
                if (password.isEmpty()) {
                    jedisPool = new JedisPool(config, host, port, 3000);
                } else {
                    jedisPool = new JedisPool(config, host, port, 3000, password);
                }
                
                try (Jedis jedis = jedisPool.getResource()) {
                    String pong = jedis.ping();
                    System.out.println("✅ 连接成功: " + pong);
                    
                    // 检查并修复只读状态
                    if (checkAndFixReadOnlyMode(jedis)) {
                        System.out.println("✅ Redis准备就绪，可以正常读写");
                        return true;
                    }
                }
            } catch (Exception ex) {
                System.out.println("❌ 连接失败: " + ex.getMessage());
                if (jedisPool != null) {
                    jedisPool.close();
                    jedisPool = null;
                }
            }
        }
        
        System.err.println("❌ 无法连接到任何Redis实例");
        return false;
    }
    
    /**
     * 检查并修复只读模式
     */
    private boolean checkAndFixReadOnlyMode(Jedis jedis) {
        try {
            System.out.println("🔍 检查Redis状态...");
            
            // 先尝试写入测试
            try {
                jedis.set("test:write", "test");
                jedis.del("test:write");
                System.out.println("✅ 写入测试成功，Redis可正常使用");
                return true;
            } catch (redis.clients.jedis.exceptions.JedisDataException e) {
                if (e.getMessage().contains("READONLY")) {
                    System.out.println("⚠️  检测到只读模式，开始修复...");
                    
                    // 获取复制信息
                    String info = jedis.info("replication");
                    if (info.contains("role:slave")) {
                        System.out.println("📋 当前角色：从节点（只读）");
                        System.out.println("🔧 执行修复：将从节点提升为主节点...");
                        
                        String result = jedis.slaveofNoOne();
                        if ("OK".equals(result)) {
                            System.out.println("✅ 提升命令执行成功");
                            
                            // 等待角色切换
                            Thread.sleep(1000);
                            
                            // 验证修复结果
                            String newInfo = jedis.info("replication");
                            if (newInfo.contains("role:master")) {
                                System.out.println("✅ 修复成功！现在是主节点");
                                
                                // 再次测试写入
                                jedis.set("test:write", "test");
                                jedis.del("test:write");
                                System.out.println("✅ 写入功能确认正常");
                                return true;
                            } else {
                                System.out.println("❌ 角色切换失败");
                                return false;
                            }
                        } else {
                            System.out.println("❌ 提升命令失败: " + result);
                            return false;
                        }
                    } else {
                        System.out.println("❌ 未知的只读原因");
                        return false;
                    }
                } else {
                    System.out.println("❌ 其他写入错误: " + e.getMessage());
                    return false;
                }
            }
        } catch (Exception e) {
            System.out.println("❌ 检查过程出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 初始化集群槽位分配
     */
    private void initializeClusterSlots() {
        String[] nodes = {"node1", "node2", "node3"};
        int slotsPerNode = 16384 / 3;
        
        for (int i = 0; i < nodes.length; i++) {
            String node = nodes[i];
            int startSlot = i * slotsPerNode;
            int endSlot = (i == nodes.length - 1) ? 16383 : (i + 1) * slotsPerNode - 1;
            
            for (int slot = startSlot; slot <= endSlot; slot++) {
                slotToNode.put(slot, node);
            }
        }
    }
    
    // ==================== 主从复制演示 ====================
    
    public String masterSlaveWrite(String key, String value) {
        System.out.println("\n=== 主从复制模式 - 写操作 ===");
        
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set("master:" + key, value);
            masterData.put(key, value);
            
            // 模拟异步复制
            new Thread(() -> {
                try {
                    Thread.sleep(100);
                    slaveData.put(key, value);
                    System.out.println("📋 数据已复制到从节点: " + key + " = " + value);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
            
            System.out.println("✅ 主节点写入成功: " + key + " = " + value);
            return result;
        } catch (Exception e) {
            System.err.println("❌ 写入失败: " + e.getMessage());
            throw new RuntimeException("写入失败", e);
        }
    }
    
    public String masterSlaveRead(String key) {
        System.out.println("\n=== 主从复制模式 - 读操作 ===");
        
        // 随机从主节点或从节点读取
        boolean readFromSlave = random.nextBoolean();
        
        if (readFromSlave && slaveData.containsKey(key)) {
            String value = slaveData.get(key);
            System.out.println("📖 从从节点读取: " + key + " = " + value);
            return value;
        } else {
            try (Jedis jedis = jedisPool.getResource()) {
                String value = jedis.get("master:" + key);
                System.out.println("📖 从主节点读取: " + key + " = " + value);
                return value;
            }
        }
    }
    
    // ==================== 哨兵模式演示 ====================
    
    public String sentinelWrite(String key, String value) {
        System.out.println("\n=== 哨兵模式 - 写操作 ===");
        
        if (!masterAvailable) {
            System.out.println("🔄 主节点不可用，触发故障转移...");
            performFailover();
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set("sentinel:" + key, value);
            System.out.println("✅ 哨兵模式写入成功 [" + currentMaster + "]: " + key + " = " + value);
            return result;
        } catch (Exception e) {
            System.err.println("❌ 写入失败: " + e.getMessage());
            throw new RuntimeException("哨兵写入失败", e);
        }
    }
    
    private void performFailover() {
        System.out.println("🚨 检测到主节点故障，开始故障转移...");
        String[] candidates = {"redis-slave1", "redis-slave2"};
        String newMaster = candidates[random.nextInt(candidates.length)];
        
        try {
            Thread.sleep(1000); // 模拟故障转移时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        currentMaster = newMaster;
        masterAvailable = true;
        System.out.println("✅ 故障转移完成，新主节点: " + currentMaster);
    }
    
    public void simulateMasterFailure() {
        System.out.println("\n=== 模拟主节点故障 ===");
        masterAvailable = false;
        System.out.println("💥 主节点 " + currentMaster + " 故障");
    }
    
    // ==================== 集群模式演示 ====================
    
    public String clusterWrite(String key, String value) {
        System.out.println("\n=== 集群模式 - 写操作 ===");
        
        int slot = calculateSlot(key);
        String targetNode = slotToNode.get(slot);
        
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set("cluster:" + key, value);
            System.out.println("✅ 集群写入成功 [" + targetNode + ", 槽位:" + slot + "]: " + key + " = " + value);
            return result;
        } catch (Exception e) {
            System.err.println("❌ 集群写入失败: " + e.getMessage());
            throw new RuntimeException("集群写入失败", e);
        }
    }
    
    public String clusterRead(String key) {
        System.out.println("\n=== 集群模式 - 读操作 ===");
        
        int slot = calculateSlot(key);
        String targetNode = slotToNode.get(slot);
        
        try (Jedis jedis = jedisPool.getResource()) {
            String value = jedis.get("cluster:" + key);
            System.out.println("📖 集群读取 [" + targetNode + ", 槽位:" + slot + "]: " + key + " = " + value);
            return value;
        }
    }
    
    private int calculateSlot(String key) {
        return Math.abs(key.hashCode()) % 16384;
    }
    
    // ==================== 演示主程序 ====================
    
    public void runAllDemos() {
        System.out.println("🎬 开始三种架构演示\n");
        
        try {
            // 1. 主从复制演示
//            System.out.println("=" .repeat(60));
            System.out.println("📋 主从复制架构演示");
//            System.out.println("=" .repeat(60));
            
            masterSlaveWrite("user:1001", "张三");
            masterSlaveWrite("user:1002", "李四");
            Thread.sleep(200); // 等待复制
            masterSlaveRead("user:1001");
            masterSlaveRead("user:1002");
            
            // 2. 哨兵模式演示
//            System.out.println("\n" + "=" .repeat(60));
            System.out.println("🛡️  哨兵模式架构演示");
//            System.out.println("=" .repeat(60));
            
            sentinelWrite("order:2001", "订单1");
            simulateMasterFailure();
            sentinelWrite("order:2002", "订单2");
            
            // 3. 集群模式演示
//            System.out.println("\n" + "=" .repeat(60));
            System.out.println("🔗 集群模式架构演示");
//            System.out.println("=" .repeat(60));
            
            clusterWrite("product:3001", "手机");
            clusterWrite("product:3002", "电脑");
            clusterRead("product:3001");
            clusterRead("product:3002");
            
            System.out.println("\n✅ 所有架构演示完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 演示过程中出错: " + e.getMessage());
        }
    }
    
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            System.out.println("🔒 Redis连接已关闭");
        }
    }
    
    public static void main(String[] args) {
        SingleRedisDemoFixed demo = new SingleRedisDemoFixed();
        
        try {
            demo.runAllDemos();
        } finally {
            demo.close();
        }
    }
}